var classApplyDescriptorDestructureSet = require("./classApplyDescriptorDestructureSet.js");
var assertClassBrand = require("./assertClassBrand.js");
var classCheckPrivateStaticFieldDescriptor = require("./classCheckPrivateStaticFieldDescriptor.js");
function _classStaticPrivateFieldDestructureSet(t, r, s) {
  return assertClassBrand(r, t), classCheckPrivateStaticFieldDescriptor(s, "set"), classApplyDescriptorDestructureSet(t, s);
}
module.exports = _classStaticPrivateFieldDestructureSet, module.exports.__esModule = true, module.exports["default"] = module.exports;