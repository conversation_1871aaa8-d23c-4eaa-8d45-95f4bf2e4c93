{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: 'SAR',\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatNumber(number: number): string {\n  return new Intl.NumberFormat('ar-SA').format(number)\n}\n\nexport function generateInvoiceNumber(prefix: string = 'INV'): string {\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}-${timestamp}-${random}`\n}\n\nexport function generateAccountCode(parentCode: string, sequence: number): string {\n  return `${parentCode}${sequence.toString().padStart(2, '0')}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,sBAAsB,SAAiB,KAAK;IAC1D,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,SAAS,oBAAoB,UAAkB,EAAE,QAAgB;IACtE,OAAO,GAAG,aAAa,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC/D", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-center\",\n          {\n            \"bg-blue-600 text-white hover:bg-blue-700\": variant === 'default',\n            \"bg-red-600 text-white hover:bg-red-700\": variant === 'destructive',\n            \"border border-gray-300 bg-white hover:bg-gray-50\": variant === 'outline',\n            \"bg-gray-100 text-gray-900 hover:bg-gray-200\": variant === 'secondary',\n            \"hover:bg-gray-100 hover:text-gray-900\": variant === 'ghost',\n            \"text-blue-600 underline-offset-4 hover:underline\": variant === 'link',\n          },\n          {\n            \"h-10 px-4 py-2\": size === 'default',\n            \"h-9 rounded-md px-3\": size === 'sm',\n            \"h-11 rounded-md px-8\": size === 'lg',\n            \"h-10 w-10\": size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sSACA;YACE,4CAA4C,YAAY;YACxD,0CAA0C,YAAY;YACtD,oDAAoD,YAAY;YAChE,+CAA+C,YAAY;YAC3D,yCAAyC,YAAY;YACrD,oDAAoD,YAAY;QAClE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/app/test/page.tsx"], "sourcesContent": ["'use client'\n\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { Button } from '@/components/ui/Button'\n\nexport default function TestPage() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12 px-4\" dir=\"rtl\">\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n            نظام إنجاز ERP\n          </h1>\n          <p className=\"text-xl text-gray-600\">\n            نظام تخطيط موارد المؤسسة - صفحة اختبار\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">المبيعات</CardTitle>\n              <CardDescription className=\"text-center\">\n                إدارة فواتير المبيعات والعملاء\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                فواتير المبيعات\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">المشتريات</CardTitle>\n              <CardDescription className=\"text-center\">\n                إدارة فواتير المشتريات والموردين\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                فواتير المشتريات\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">المخزون</CardTitle>\n              <CardDescription className=\"text-center\">\n                إدارة المنتجات والمخزون\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                إدارة المخزون\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">العملاء</CardTitle>\n              <CardDescription className=\"text-center\">\n                إدارة بيانات العملاء\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                قائمة العملاء\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">الموردين</CardTitle>\n              <CardDescription className=\"text-center\">\n                إدارة بيانات الموردين\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                قائمة الموردين\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-center\">التقارير</CardTitle>\n              <CardDescription className=\"text-center\">\n                التقارير المالية والإحصائيات\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\">\n                عرض التقارير\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        <div className=\"mt-12 text-center\">\n          <Card className=\"max-w-2xl mx-auto\">\n            <CardHeader>\n              <CardTitle>حالة النظام</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center\">\n                  <span>إطار العمل:</span>\n                  <span className=\"font-semibold\">Next.js 15</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span>قاعدة البيانات:</span>\n                  <span className=\"font-semibold\">Supabase</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span>التصميم:</span>\n                  <span className=\"font-semibold\">Tailwind CSS</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span>اللغة:</span>\n                  <span className=\"font-semibold\">TypeScript</span>\n                </div>\n                <div className=\"flex justify-between items-center\">\n                  <span>الحالة:</span>\n                  <span className=\"font-semibold text-green-600\">يعمل بنجاح ✓</span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;QAAqC,KAAI;kBACtD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAKvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;sCAM/B,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;;sDACT,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAc;;;;;;sDACnC,8OAAC,gIAAA,CAAA,kBAAe;4CAAC,WAAU;sDAAc;;;;;;;;;;;;8CAI3C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOjC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE", "debugId": null}}]}