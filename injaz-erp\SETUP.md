# دليل إعداد نظام إنجاز ERP

## خطوات الإعداد الكاملة

### 1. إعداد Supabase

#### إنشاء مشروع جديد
1. اذهب إلى [Supabase](https://supabase.com)
2. قم بإنشاء حساب جديد أو تسجيل الدخول
3. انقر على "New Project"
4. اختر Organization
5. أدخل اسم المشروع: "Injaz ERP"
6. أدخل كلمة مرور قاعدة البيانات (احفظها في مكان آمن)
7. اختر المنطقة الأقرب لك
8. انقر على "Create new project"

#### الحصول على بيانات الاتصال
1. اذهب إلى Settings > API
2. انسخ:
   - Project URL
   - anon public key

### 2. إعداد المشروع المحلي

#### تثبيت المكتبات
```bash
cd injaz-erp
npm install
```

#### إعداد متغيرات البيئة
1. انسخ ملف البيئة:
```bash
cp .env.local.example .env.local
```

2. حدث ملف `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

### 3. إعداد قاعدة البيانات

#### تشغيل ملف إنشاء الجداول
1. اذهب إلى Supabase Dashboard
2. انقر على "SQL Editor"
3. انقر على "New query"
4. انسخ محتوى ملف `database/schema.sql` كاملاً
5. الصق المحتوى في المحرر
6. انقر على "Run" لتشغيل الاستعلام

#### تشغيل البيانات الأولية
1. في SQL Editor، انشئ استعلام جديد
2. انسخ محتوى ملف `database/seed.sql` كاملاً
3. الصق المحتوى في المحرر
4. انقر على "Run" لتشغيل الاستعلام

### 4. إعداد المصادقة

#### تفعيل Email Authentication
1. اذهب إلى Authentication > Settings
2. تأكد من تفعيل "Enable email confirmations"
3. في "Site URL" أدخل: `http://localhost:3000`
4. في "Redirect URLs" أضف: `http://localhost:3000/auth/callback`

#### إعداد Row Level Security (RLS)
الجداول محمية بـ RLS افتراضياً. لإضافة سياسات الأمان:

```sql
-- سياسة للمستخدمين
CREATE POLICY "Users can view own profile" ON users
FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
FOR UPDATE USING (auth.uid() = id);

-- سياسة للحسابات (يمكن للجميع القراءة)
CREATE POLICY "Anyone can view accounts" ON accounts
FOR SELECT USING (true);

-- سياسة للعملاء (يمكن للمستخدمين المصادق عليهم فقط)
CREATE POLICY "Authenticated users can view customers" ON customers
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can insert customers" ON customers
FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can update customers" ON customers
FOR UPDATE USING (auth.role() = 'authenticated');

-- نفس الشيء للموردين والمنتجات والفواتير
CREATE POLICY "Authenticated users can view suppliers" ON suppliers
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view products" ON products
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view sales_invoices" ON sales_invoices
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view purchase_invoices" ON purchase_invoices
FOR SELECT USING (auth.role() = 'authenticated');

CREATE POLICY "Authenticated users can view journal_entries" ON journal_entries
FOR SELECT USING (auth.role() = 'authenticated');
```

### 5. إنشاء أول مستخدم

#### التسجيل من التطبيق
1. شغل المشروع: `npm run dev`
2. اذهب إلى `http://localhost:3000`
3. ستظهر صفحة تسجيل الدخول
4. قم بالتسجيل بإيميل وكلمة مرور

#### ترقية المستخدم إلى Admin
1. اذهب إلى Supabase Dashboard > Authentication > Users
2. انسخ User ID للمستخدم الجديد
3. اذهب إلى SQL Editor وشغل:

```sql
UPDATE users 
SET role = 'admin' 
WHERE id = 'user-id-here';
```

### 6. اختبار النظام

#### التحقق من الاتصال
1. سجل دخول بالمستخدم الجديد
2. يجب أن تظهر لوحة التحكم
3. تحقق من ظهور القوائم الجانبية
4. جرب الانتقال بين الصفحات

#### اختبار البيانات
1. اذهب إلى المحاسبة > شجرة الحسابات
2. يجب أن تظهر الحسابات الأساسية
3. جرب إضافة عميل جديد
4. جرب إضافة منتج جديد

### 7. إعدادات إضافية (اختيارية)

#### إعداد البريد الإلكتروني
لإرسال رسائل التأكيد:
1. اذهب إلى Settings > Auth
2. في "SMTP Settings" أدخل بيانات خادم البريد
3. أو استخدم خدمة مثل SendGrid

#### إعداد التخزين
لرفع الملفات:
1. اذهب إلى Storage
2. انشئ bucket جديد باسم "documents"
3. اضبط سياسات الوصول حسب الحاجة

### 8. استكشاف الأخطاء

#### مشاكل شائعة:

**خطأ في الاتصال بقاعدة البيانات:**
- تحقق من صحة URL و API Key
- تأكد من أن المشروع نشط في Supabase

**خطأ في المصادقة:**
- تحقق من إعدادات Site URL
- تأكد من تفعيل Email Authentication

**خطأ في الأذونات:**
- تحقق من سياسات RLS
- تأكد من أن المستخدم له دور صحيح

**خطأ في عرض البيانات:**
- تحقق من تشغيل ملفات SQL بنجاح
- تأكد من وجود البيانات في الجداول

### 9. النشر (Production)

#### إعداد Vercel
1. ادفع الكود إلى GitHub
2. اربط المشروع بـ Vercel
3. أضف متغيرات البيئة في Vercel
4. حدث Site URL في Supabase

#### إعدادات الأمان
1. فعل SSL في Supabase
2. اضبط CORS settings
3. حدث Redirect URLs للدومين الجديد
4. فعل Rate Limiting

---

بعد اتباع هذه الخطوات، سيكون نظام إنجاز ERP جاهزاً للاستخدام! 🎉
