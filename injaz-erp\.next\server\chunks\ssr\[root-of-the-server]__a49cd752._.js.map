{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: 'SAR',\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatNumber(number: number): string {\n  return new Intl.NumberFormat('ar-SA').format(number)\n}\n\nexport function generateInvoiceNumber(prefix: string = 'INV'): string {\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}-${timestamp}-${random}`\n}\n\nexport function generateAccountCode(parentCode: string, sequence: number): string {\n  return `${parentCode}${sequence.toString().padStart(2, '0')}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,sBAAsB,SAAiB,KAAK;IAC1D,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,SAAS,oBAAoB,UAAkB,EAAE,QAAgB;IACtE,OAAO,GAAG,aAAa,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC/D", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-center\",\n          {\n            \"bg-blue-600 text-white hover:bg-blue-700\": variant === 'default',\n            \"bg-red-600 text-white hover:bg-red-700\": variant === 'destructive',\n            \"border border-gray-300 bg-white hover:bg-gray-50\": variant === 'outline',\n            \"bg-gray-100 text-gray-900 hover:bg-gray-200\": variant === 'secondary',\n            \"hover:bg-gray-100 hover:text-gray-900\": variant === 'ghost',\n            \"text-blue-600 underline-offset-4 hover:underline\": variant === 'link',\n          },\n          {\n            \"h-10 px-4 py-2\": size === 'default',\n            \"h-9 rounded-md px-3\": size === 'sm',\n            \"h-11 rounded-md px-8\": size === 'lg',\n            \"h-10 w-10\": size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sSACA;YACE,4CAA4C,YAAY;YACxD,0CAA0C,YAAY;YACtD,oDAAoD,YAAY;YAChE,+CAA+C,YAAY;YAC3D,yCAAyC,YAAY;YACrD,oDAAoD,YAAY;QAClE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/auth/AuthProvider'\nimport { Button } from '@/components/ui/Button'\nimport { \n  Menu,\n  X,\n  Home,\n  Users,\n  ShoppingCart,\n  Package,\n  FileText,\n  BarChart3,\n  Settings,\n  LogOut,\n  Calculator,\n  Truck,\n  RefreshCw\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nconst navigation = [\n  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },\n  { \n    name: 'المبيعات', \n    icon: ShoppingCart,\n    children: [\n      { name: 'فواتير المبيعات', href: '/dashboard/sales/invoices' },\n      { name: 'عروض الأسعار', href: '/dashboard/sales/quotes' },\n      { name: 'أوامر البيع', href: '/dashboard/sales/orders' },\n    ]\n  },\n  { \n    name: 'المشتريات', \n    icon: Truck,\n    children: [\n      { name: 'فواتير المشتريات', href: '/dashboard/purchases/invoices' },\n      { name: 'أوامر الشراء', href: '/dashboard/purchases/orders' },\n      { name: 'استلام البضائع', href: '/dashboard/purchases/receipts' },\n    ]\n  },\n  { \n    name: 'المرتجعات', \n    icon: RefreshCw,\n    children: [\n      { name: 'مرتجعات المبيعات', href: '/dashboard/returns/sales' },\n      { name: 'مرتجعات المشتريات', href: '/dashboard/returns/purchases' },\n    ]\n  },\n  { \n    name: 'العملاء والموردين', \n    icon: Users,\n    children: [\n      { name: 'العملاء', href: '/dashboard/customers' },\n      { name: 'الموردين', href: '/dashboard/suppliers' },\n    ]\n  },\n  { \n    name: 'المخزون', \n    icon: Package,\n    children: [\n      { name: 'الأصناف', href: '/dashboard/inventory/products' },\n      { name: 'حركات المخزون', href: '/dashboard/inventory/movements' },\n      { name: 'تقارير المخزون', href: '/dashboard/inventory/reports' },\n    ]\n  },\n  { \n    name: 'المحاسبة', \n    icon: Calculator,\n    children: [\n      { name: 'شجرة الحسابات', href: '/dashboard/accounting/accounts' },\n      { name: 'القيود اليومية', href: '/dashboard/accounting/journal' },\n      { name: 'دفتر الأستاذ', href: '/dashboard/accounting/ledger' },\n      { name: 'الحسابات الختامية', href: '/dashboard/accounting/final-accounts' },\n    ]\n  },\n  { \n    name: 'التقارير', \n    icon: BarChart3,\n    children: [\n      { name: 'التقارير المالية', href: '/dashboard/reports/financial' },\n      { name: 'تقارير المبيعات', href: '/dashboard/reports/sales' },\n      { name: 'تقارير المشتريات', href: '/dashboard/reports/purchases' },\n      { name: 'تقارير المخزون', href: '/dashboard/reports/inventory' },\n    ]\n  },\n  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },\n]\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\n  const { signOut, userProfile } = useAuth()\n  const pathname = usePathname()\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemName) \n        ? prev.filter(item => item !== itemName)\n        : [...prev, itemName]\n    )\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 right-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <h2 className=\"text-lg font-semibold\">نظام إنجاز ERP</h2>\n            <Button variant=\"ghost\" size=\"icon\" onClick={() => setSidebarOpen(false)}>\n              <X className=\"h-6 w-6\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-4 py-4\">\n            {navigation.map((item) => (\n              <div key={item.name}>\n                {item.children ? (\n                  <div>\n                    <button\n                      onClick={() => toggleExpanded(item.name)}\n                      className=\"flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100\"\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <item.icon className=\"h-5 w-5\" />\n                        {item.name}\n                      </div>\n                    </button>\n                    {expandedItems.includes(item.name) && (\n                      <div className=\"mr-8 mt-1 space-y-1\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.href}\n                            href={child.href}\n                            className={`block rounded-md px-3 py-2 text-sm ${\n                              pathname === child.href\n                                ? 'bg-blue-100 text-blue-700'\n                                : 'text-gray-600 hover:bg-gray-100'\n                            }`}\n                          >\n                            {child.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${\n                      pathname === item.href\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:flex lg:w-72 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-l border-gray-200 bg-white px-6 shadow-lg\">\n          <div className=\"flex h-16 shrink-0 items-center justify-center\">\n            <h1 className=\"text-xl font-bold text-gray-900 text-center\">نظام إنجاز ERP</h1>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      {item.children ? (\n                        <div>\n                          <button\n                            onClick={() => toggleExpanded(item.name)}\n                            className=\"flex w-full items-center justify-between rounded-md p-2 text-sm font-semibold leading-6 text-gray-700 hover:bg-gray-50\"\n                          >\n                            <div className=\"flex items-center gap-x-3\">\n                              <item.icon className=\"h-6 w-6 shrink-0\" />\n                              <span>{item.name}</span>\n                            </div>\n                            <span className=\"text-xs\">\n                              {expandedItems.includes(item.name) ? '▼' : '◀'}\n                            </span>\n                          </button>\n                          {expandedItems.includes(item.name) && (\n                            <ul className=\"mr-9 mt-1 space-y-1\">\n                              {item.children.map((child) => (\n                                <li key={child.href}>\n                                  <Link\n                                    href={child.href}\n                                    className={`block rounded-md py-2 pr-2 text-sm leading-6 ${\n                                      pathname === child.href\n                                        ? 'bg-blue-50 text-blue-600'\n                                        : 'text-gray-700 hover:bg-gray-50'\n                                    }`}\n                                  >\n                                    {child.name}\n                                  </Link>\n                                </li>\n                              ))}\n                            </ul>\n                          )}\n                        </div>\n                      ) : (\n                        <Link\n                          href={item.href}\n                          className={`group flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 items-center ${\n                            pathname === item.href\n                              ? 'bg-blue-50 text-blue-600'\n                              : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n                          }`}\n                        >\n                          <item.icon className=\"h-6 w-6 shrink-0\" />\n                          <span>{item.name}</span>\n                        </Link>\n                      )}\n                    </li>\n                  ))}\n                </ul>\n              </li>\n              <li className=\"mt-auto\">\n                <div className=\"flex items-center gap-x-4 px-2 py-3 text-sm font-semibold leading-6 text-gray-900\">\n                  <div className=\"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center\">\n                    <span className=\"text-white text-sm\">\n                      {userProfile?.full_name?.charAt(0) || 'U'}\n                    </span>\n                  </div>\n                  <span className=\"sr-only\">Your profile</span>\n                  <span aria-hidden=\"true\">{userProfile?.full_name}</span>\n                </div>\n                <Button\n                  variant=\"ghost\"\n                  onClick={handleSignOut}\n                  className=\"w-full justify-start gap-x-3 px-2\"\n                >\n                  <LogOut className=\"h-6 w-6 shrink-0\" />\n                  تسجيل الخروج\n                </Button>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pr-72\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"lg:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            <div className=\"flex flex-1\"></div>\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-200\" />\n              <div className=\"flex items-center gap-x-4\">\n                <span className=\"text-sm font-medium text-gray-700\">\n                  {userProfile?.full_name}\n                </span>\n                <div className=\"h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center\">\n                  <span className=\"text-white text-sm\">\n                    {userProfile?.full_name?.charAt(0) || 'U'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-10\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;AArBA;;;;;;;;AA2BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAe,MAAM;QAAc,MAAM,mMAAA,CAAA,OAAI;IAAC;IACtD;QACE,MAAM;QACN,MAAM,sNAAA,CAAA,eAAY;QAClB,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;YAA4B;YAC7D;gBAAE,MAAM;gBAAgB,MAAM;YAA0B;YACxD;gBAAE,MAAM;gBAAe,MAAM;YAA0B;SACxD;IACH;IACA;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAAgC;YAClE;gBAAE,MAAM;gBAAgB,MAAM;YAA8B;YAC5D;gBAAE,MAAM;gBAAkB,MAAM;YAAgC;SACjE;IACH;IACA;QACE,MAAM;QACN,MAAM,gNAAA,CAAA,YAAS;QACf,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAA2B;YAC7D;gBAAE,MAAM;gBAAqB,MAAM;YAA+B;SACnE;IACH;IACA;QACE,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAuB;YAChD;gBAAE,MAAM;gBAAY,MAAM;YAAuB;SAClD;IACH;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAgC;YACzD;gBAAE,MAAM;gBAAiB,MAAM;YAAiC;YAChE;gBAAE,MAAM;gBAAkB,MAAM;YAA+B;SAChE;IACH;IACA;QACE,MAAM;QACN,MAAM,8MAAA,CAAA,aAAU;QAChB,UAAU;YACR;gBAAE,MAAM;gBAAiB,MAAM;YAAiC;YAChE;gBAAE,MAAM;gBAAkB,MAAM;YAAgC;YAChE;gBAAE,MAAM;gBAAgB,MAAM;YAA+B;YAC7D;gBAAE,MAAM;gBAAqB,MAAM;YAAuC;SAC3E;IACH;IACA;QACE,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAA+B;YACjE;gBAAE,MAAM;gBAAmB,MAAM;YAA2B;YAC5D;gBAAE,MAAM;gBAAoB,MAAM;YAA+B;YACjE;gBAAE,MAAM;gBAAkB,MAAM;YAA+B;SAChE;IACH;IACA;QAAE,MAAM;QAAa,MAAM;QAAuB,MAAM,0MAAA,CAAA,WAAQ;IAAC;CAClE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IACvC,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,8OAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,SAAS,IAAM,eAAe;kDAChE,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;kDACE,KAAK,QAAQ,iBACZ,8OAAC;;8DACC,8OAAC;oDACC,SAAS,IAAM,eAAe,KAAK,IAAI;oDACvC,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;4DACpB,KAAK,IAAI;;;;;;;;;;;;gDAGb,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,8OAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC,4JAAA,CAAA,UAAI;4DAEH,MAAM,MAAM,IAAI;4DAChB,WAAW,CAAC,mCAAmC,EAC7C,aAAa,MAAM,IAAI,GACnB,8BACA,mCACJ;sEAED,MAAM,IAAI;2DARN,MAAM,IAAI;;;;;;;;;;;;;;;iEAezB,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,iEAAiE,EAC3E,aAAa,KAAK,IAAI,GAClB,8BACA,mCACJ;;8DAEF,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAxCN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAkD3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAA8C;;;;;;;;;;;sCAE9D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,MAAK;gCAAO,WAAU;;kDACxB,8OAAC;kDACC,cAAA,8OAAC;4CAAG,MAAK;4CAAO,WAAU;sDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;8DACE,KAAK,QAAQ,iBACZ,8OAAC;;0EACC,8OAAC;gEACC,SAAS,IAAM,eAAe,KAAK,IAAI;gEACvC,WAAU;;kFAEV,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;0FACrB,8OAAC;0FAAM,KAAK,IAAI;;;;;;;;;;;;kFAElB,8OAAC;wEAAK,WAAU;kFACb,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,MAAM;;;;;;;;;;;;4DAG9C,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,8OAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,8OAAC;kFACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,MAAM,IAAI;4EAChB,WAAW,CAAC,6CAA6C,EACvD,aAAa,MAAM,IAAI,GACnB,6BACA,kCACJ;sFAED,MAAM,IAAI;;;;;;uEATN,MAAM,IAAI;;;;;;;;;;;;;;;6EAiB3B,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,+EAA+E,EACzF,aAAa,KAAK,IAAI,GAClB,6BACA,sDACJ;;0EAEF,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAM,KAAK,IAAI;;;;;;;;;;;;mDA5Cb,KAAK,IAAI;;;;;;;;;;;;;;;kDAmDxB,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,aAAa,WAAW,OAAO,MAAM;;;;;;;;;;;kEAG1C,8OAAC;wDAAK,WAAU;kEAAU;;;;;;kEAC1B,8OAAC;wDAAK,eAAY;kEAAQ,aAAa;;;;;;;;;;;;0DAEzC,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,aAAa;;;;;;kEAEhB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEACb,aAAa,WAAW,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlD,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 796, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/components/auth/AuthProvider'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { DashboardLayout } from '@/components/layout/DashboardLayout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { \n  Users, \n  ShoppingCart, \n  Package, \n  TrendingUp, \n  DollarSign,\n  FileText,\n  AlertTriangle,\n  BarChart3\n} from 'lucide-react'\n\nexport default function Dashboard() {\n  const { user, userProfile, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!user && !loading) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"mt-4 text-lg text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Section */}\n        <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg shadow-lg\">\n          <h1 className=\"text-3xl font-bold mb-2 text-center\">\n            مرحباً، {userProfile?.full_name || user.email}\n          </h1>\n          <p className=\"text-blue-100 text-center text-lg\">\n            مرحباً بك في نظام إنجاز ERP - نظام تخطيط موارد المؤسسة\n          </p>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">إجمالي المبيعات</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold ltr-numbers\">45,231.89 ر.س</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +20.1% من الشهر الماضي\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">عدد العملاء</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold ltr-numbers\">+2350</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +180.1% من الشهر الماضي\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">الطلبات</CardTitle>\n              <ShoppingCart className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold ltr-numbers\">+12,234</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +19% من الشهر الماضي\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">المخزون</CardTitle>\n              <Package className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold ltr-numbers\">573</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +201 منذ الساعة الماضية\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <FileText className=\"h-5 w-5\" />\n                فاتورة مبيعات جديدة\n              </CardTitle>\n              <CardDescription>\n                إنشاء فاتورة مبيعات جديدة للعملاء\n              </CardDescription>\n            </CardHeader>\n          </Card>\n\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <ShoppingCart className=\"h-5 w-5\" />\n                فاتورة مشتريات جديدة\n              </CardTitle>\n              <CardDescription>\n                إنشاء فاتورة مشتريات جديدة من الموردين\n              </CardDescription>\n            </CardHeader>\n          </Card>\n\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <BarChart3 className=\"h-5 w-5\" />\n                التقارير المالية\n              </CardTitle>\n              <CardDescription>\n                عرض التقارير المالية والمحاسبية\n              </CardDescription>\n            </CardHeader>\n          </Card>\n        </div>\n\n        {/* Recent Activities */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>الأنشطة الأخيرة</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">تم إنشاء فاتورة مبيعات جديدة</p>\n                    <p className=\"text-xs text-muted-foreground\">منذ 5 دقائق</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">تم إضافة عميل جديد</p>\n                    <p className=\"text-xs text-muted-foreground\">منذ 15 دقيقة</p>\n                  </div>\n                </div>\n                <div className=\"flex items-center gap-3\">\n                  <div className=\"w-2 h-2 bg-orange-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">تحديث المخزون</p>\n                    <p className=\"text-xs text-muted-foreground\">منذ 30 دقيقة</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <AlertTriangle className=\"h-5 w-5 text-orange-500\" />\n                تنبيهات المخزون\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-3 bg-orange-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium\">منتج A</p>\n                    <p className=\"text-xs text-muted-foreground\">الكمية المتبقية: 5</p>\n                  </div>\n                  <span className=\"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded\">\n                    مخزون منخفض\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between p-3 bg-red-50 rounded-lg\">\n                  <div>\n                    <p className=\"text-sm font-medium\">منتج B</p>\n                    <p className=\"text-xs text-muted-foreground\">الكمية المتبقية: 0</p>\n                  </div>\n                  <span className=\"text-xs bg-red-100 text-red-800 px-2 py-1 rounded\">\n                    نفد المخزون\n                  </span>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAkBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,0IAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,SAAS;YACrB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,8OAAC,+IAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCAAsC;gCACzC,aAAa,aAAa,KAAK,KAAK;;;;;;;sCAE/C,8OAAC;4BAAE,WAAU;sCAAoC;;;;;;;;;;;;8BAMnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;8CAEnB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;8CAE1B,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAC3C,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;8CAErB,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;sDAChD,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAQnD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;sCAMrB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGtC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;sCAMrB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGnC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;;;;;;;;;;;;8BAQvB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOvD,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAA4B;;;;;;;;;;;;8CAIzD,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,8OAAC;wDAAK,WAAU;kEAA0D;;;;;;;;;;;;0DAI5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;kEAE/C,8OAAC;wDAAK,WAAU;kEAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtF", "debugId": null}}]}