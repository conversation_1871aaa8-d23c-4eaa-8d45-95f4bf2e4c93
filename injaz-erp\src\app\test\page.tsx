'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4" dir="rtl">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            نظام إنجاز ERP
          </h1>
          <p className="text-xl text-gray-600">
            نظام تخطيط موارد المؤسسة - صفحة اختبار
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">المبيعات</CardTitle>
              <CardDescription className="text-center">
                إدارة فواتير المبيعات والعملاء
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                فواتير المبيعات
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">المشتريات</CardTitle>
              <CardDescription className="text-center">
                إدارة فواتير المشتريات والموردين
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                فواتير المشتريات
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">المخزون</CardTitle>
              <CardDescription className="text-center">
                إدارة المنتجات والمخزون
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                إدارة المخزون
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">العملاء</CardTitle>
              <CardDescription className="text-center">
                إدارة بيانات العملاء
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                قائمة العملاء
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">الموردين</CardTitle>
              <CardDescription className="text-center">
                إدارة بيانات الموردين
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                قائمة الموردين
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-center">التقارير</CardTitle>
              <CardDescription className="text-center">
                التقارير المالية والإحصائيات
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button className="w-full">
                عرض التقارير
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="mt-12 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>حالة النظام</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>إطار العمل:</span>
                  <span className="font-semibold">Next.js 15</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>قاعدة البيانات:</span>
                  <span className="font-semibold">Supabase</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>التصميم:</span>
                  <span className="font-semibold">Tailwind CSS</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>اللغة:</span>
                  <span className="font-semibold">TypeScript</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>الحالة:</span>
                  <span className="font-semibold text-green-600">يعمل بنجاح ✓</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
