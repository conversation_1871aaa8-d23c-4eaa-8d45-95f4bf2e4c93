'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import {
  ShoppingCart,
  Package,
  Users,
  Truck,
  BarChart3,
  Calculator,
  TrendingUp,
  DollarSign,
  FileText,
  Settings
} from 'lucide-react'

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="bg-blue-600 p-3 rounded-lg">
                <Calculator className="h-8 w-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">نظام إنجاز ERP</h1>
                <p className="text-gray-600">نظام تخطيط موارد المؤسسة</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 ml-2" />
                الإعدادات
              </Button>
              <Button size="sm">
                <FileText className="h-4 w-4 ml-2" />
                التقارير
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm font-medium">إجمالي المبيعات</p>
                  <p className="text-3xl font-bold">245,000 ر.س</p>
                  <p className="text-blue-100 text-sm">+12% من الشهر الماضي</p>
                </div>
                <DollarSign className="h-12 w-12 text-blue-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm font-medium">عدد العملاء</p>
                  <p className="text-3xl font-bold">1,234</p>
                  <p className="text-green-100 text-sm">+5% من الشهر الماضي</p>
                </div>
                <Users className="h-12 w-12 text-green-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm font-medium">المنتجات</p>
                  <p className="text-3xl font-bold">567</p>
                  <p className="text-purple-100 text-sm">+8 منتجات جديدة</p>
                </div>
                <Package className="h-12 w-12 text-purple-200" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm font-medium">الطلبات</p>
                  <p className="text-3xl font-bold">89</p>
                  <p className="text-orange-100 text-sm">+15% من الأسبوع الماضي</p>
                </div>
                <TrendingUp className="h-12 w-12 text-orange-200" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Modules */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-blue-100 p-3 rounded-lg">
                  <ShoppingCart className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">المبيعات</CardTitle>
                  <CardDescription className="text-gray-600">
                    إدارة فواتير المبيعات والعملاء
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 ml-2" />
                  فواتير المبيعات
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة العملاء
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-green-100 p-3 rounded-lg">
                  <Truck className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">المشتريات</CardTitle>
                  <CardDescription className="text-gray-600">
                    إدارة فواتير المشتريات والموردين
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 ml-2" />
                  فواتير المشتريات
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Truck className="h-4 w-4 ml-2" />
                  إدارة الموردين
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-purple-100 p-3 rounded-lg">
                  <Package className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">المخزون</CardTitle>
                  <CardDescription className="text-gray-600">
                    إدارة المنتجات والمخزون
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Package className="h-4 w-4 ml-2" />
                  إدارة المنتجات
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  تقارير المخزون
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-orange-100 p-3 rounded-lg">
                  <Calculator className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">المحاسبة</CardTitle>
                  <CardDescription className="text-gray-600">
                    النظام المحاسبي والقيود
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Calculator className="h-4 w-4 ml-2" />
                  شجرة الحسابات
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <FileText className="h-4 w-4 ml-2" />
                  القيود اليومية
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-indigo-100 p-3 rounded-lg">
                  <BarChart3 className="h-6 w-6 text-indigo-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">التقارير</CardTitle>
                  <CardDescription className="text-gray-600">
                    التقارير المالية والإحصائيات
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  التقارير المالية
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <TrendingUp className="h-4 w-4 ml-2" />
                  تقارير المبيعات
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-shadow duration-300 border-0 shadow-md">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="bg-gray-100 p-3 rounded-lg">
                  <Settings className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <CardTitle className="text-lg font-semibold text-gray-900">الإعدادات</CardTitle>
                  <CardDescription className="text-gray-600">
                    إعدادات النظام والمستخدمين
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Users className="h-4 w-4 ml-2" />
                  إدارة المستخدمين
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Settings className="h-4 w-4 ml-2" />
                  إعدادات النظام
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* System Status */}
        <Card className="border-0 shadow-md">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
              <div className="bg-green-100 p-2 rounded-lg ml-3">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              حالة النظام
            </CardTitle>
            <CardDescription>
              معلومات تقنية حول النظام والتقنيات المستخدمة
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">إطار العمل:</span>
                  <span className="font-bold text-blue-600">Next.js 15</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">قاعدة البيانات:</span>
                  <span className="font-bold text-green-600">Supabase</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">التصميم:</span>
                  <span className="font-bold text-purple-600">Tailwind CSS</span>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">اللغة:</span>
                  <span className="font-bold text-indigo-600">TypeScript</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">الأيقونات:</span>
                  <span className="font-bold text-orange-600">Lucide React</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg border border-green-200">
                  <span className="font-medium text-gray-700">الحالة:</span>
                  <span className="font-bold text-green-600 flex items-center">
                    <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                    يعمل بنجاح ✓
                  </span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
              <div className="flex flex-wrap gap-3">
                <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                  <FileText className="h-4 w-4 ml-2" />
                  فاتورة جديدة
                </Button>
                <Button size="sm" variant="outline">
                  <Users className="h-4 w-4 ml-2" />
                  إضافة عميل
                </Button>
                <Button size="sm" variant="outline">
                  <Package className="h-4 w-4 ml-2" />
                  إضافة منتج
                </Button>
                <Button size="sm" variant="outline">
                  <BarChart3 className="h-4 w-4 ml-2" />
                  عرض التقارير
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex justify-between items-center">
            <div className="text-gray-600">
              <p>© 2024 نظام إنجاز ERP. جميع الحقوق محفوظة.</p>
            </div>
            <div className="flex space-x-4 space-x-reverse">
              <Button variant="ghost" size="sm">الدعم</Button>
              <Button variant="ghost" size="sm">التوثيق</Button>
              <Button variant="ghost" size="sm">حول النظام</Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
