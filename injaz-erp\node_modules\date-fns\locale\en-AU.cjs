"use strict";
exports.enAU = void 0;
var _index = require("./en-US/_lib/formatDistance.cjs");
var _index2 = require("./en-AU/_lib/formatLong.cjs");
var _index3 = require("./en-US/_lib/formatRelative.cjs");
var _index4 = require("./en-US/_lib/localize.cjs");
var _index5 = require("./en-US/_lib/match.cjs");

/**
 * @category Locales
 * @summary English locale (Australia).
 * @language English
 * @iso-639-2 eng
 * <AUTHOR> [@JulienMalige](https://github.com/JulienMalige)
 */
const enAU = (exports.enAU = {
  code: "en-AU",
  formatDistance: _index.formatDistance,
  formatLong: _index2.formatLong,
  formatRelative: _index3.formatRelative,
  localize: _index4.localize,
  match: _index5.match,
  options: {
    weekStartsOn: 1 /* Monday */,
    firstWeekContainsDate: 4,
  },
});
