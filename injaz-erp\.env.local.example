# Supabase Configuration
# احصل على هذه القيم من Supabase Dashboard > Settings > API
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# Optional: Service Role Key (للعمليات الإدارية فقط)
# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Optional: JWT Secret (للتوقيع المخصص)
# JWT_SECRET=your-jwt-secret

# Optional: Database URL (للاتصال المباشر)
# DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Optional: Email Configuration (إذا كنت تستخدم خدمة بريد مخصصة)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# Optional: File Upload Configuration
# MAX_FILE_SIZE=5242880  # 5MB
# ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Optional: Application Settings
# APP_NAME=نظام إنجاز ERP
# APP_VERSION=1.0.0
# COMPANY_NAME=اسم شركتك
# COMPANY_ADDRESS=عنوان شركتك
# COMPANY_PHONE=رقم هاتف شركتك
# COMPANY_EMAIL=<EMAIL>

# Optional: Currency and Localization
# DEFAULT_CURRENCY=SAR
# DEFAULT_LOCALE=ar-SA
# TIMEZONE=Asia/Riyadh

# Optional: Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_FREQUENCY=daily
# BACKUP_RETENTION_DAYS=30
