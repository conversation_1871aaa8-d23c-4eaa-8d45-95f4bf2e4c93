{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: 'SAR',\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatNumber(number: number): string {\n  return new Intl.NumberFormat('ar-SA').format(number)\n}\n\nexport function generateInvoiceNumber(prefix: string = 'INV'): string {\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}-${timestamp}-${random}`\n}\n\nexport function generateAccountCode(parentCode: string, sequence: number): string {\n  return `${parentCode}${sequence.toString().padStart(2, '0')}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,sBAAsB,SAAiB,KAAK;IAC1D,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,SAAS,oBAAoB,UAAkB,EAAE,QAAgB;IACtE,OAAO,GAAG,aAAa,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC/D", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-center shadow-sm\",\n          {\n            \"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md active:bg-blue-800\": variant === 'default',\n            \"bg-red-600 text-white hover:bg-red-700 hover:shadow-md active:bg-red-800\": variant === 'destructive',\n            \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\": variant === 'outline',\n            \"bg-gray-100 text-gray-900 hover:bg-gray-200\": variant === 'secondary',\n            \"hover:bg-gray-100 hover:text-gray-900 text-gray-700\": variant === 'ghost',\n            \"text-blue-600 underline-offset-4 hover:underline bg-transparent shadow-none\": variant === 'link',\n          },\n          {\n            \"h-10 px-4 py-2 text-sm\": size === 'default',\n            \"h-8 px-3 py-1 text-xs\": size === 'sm',\n            \"h-12 px-6 py-3 text-base\": size === 'lg',\n            \"h-10 w-10 p-0\": size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uSACA;YACE,+EAA+E,YAAY;YAC3F,4EAA4E,YAAY;YACxF,wFAAwF,YAAY;YACpG,+CAA+C,YAAY;YAC3D,uDAAuD,YAAY;YACnE,+EAA+E,YAAY;QAC7F,GACA;YACE,0BAA0B,SAAS;YACnC,yBAAyB,SAAS;YAClC,4BAA4B,SAAS;YACrC,iBAAiB,SAAS;QAC5B,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Input.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:cursor-not-allowed disabled:opacity-50 transition-colors\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uPACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white text-gray-900 shadow-sm hover:shadow-md transition-shadow duration-200\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qHACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/auth/LoginForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from './AuthProvider'\nimport { Button } from '@/components/ui/Button'\nimport { Input } from '@/components/ui/Input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\n\nexport function LoginForm() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const { signIn } = useAuth()\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const { error } = await signIn(email, password)\n      if (error) {\n        setError(error.message)\n      } else {\n        router.push('/dashboard')\n      }\n    } catch (err) {\n      setError('حدث خطأ غير متوقع')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8\" dir=\"rtl\">\n      <div className=\"w-full max-w-md\">\n        {/* Logo and Title */}\n        <div className=\"text-center mb-8\">\n          <div className=\"bg-blue-600 w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg\">\n            <svg className=\"w-8 h-8 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">نظام إنجاز ERP</h1>\n          <p className=\"text-gray-600\">نظام تخطيط موارد المؤسسة</p>\n        </div>\n\n        <Card className=\"shadow-xl border-0 backdrop-blur-sm bg-white/95\">\n          <CardHeader className=\"text-center space-y-2 pb-6\">\n            <CardTitle className=\"text-2xl font-bold text-gray-900\">\n              تسجيل الدخول\n            </CardTitle>\n            <CardDescription className=\"text-gray-600\">\n              أدخل بياناتك للوصول إلى النظام\n            </CardDescription>\n          </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {error && (\n              <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n                {error}\n              </div>\n            )}\n            \n            <div className=\"space-y-2\">\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 text-right\">\n                البريد الإلكتروني\n              </label>\n              <Input\n                id=\"email\"\n                type=\"email\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                required\n                className=\"w-full text-left\"\n                placeholder=\"أدخل البريد الإلكتروني\"\n                dir=\"ltr\"\n              />\n            </div>\n\n            <div className=\"space-y-2\">\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 text-right\">\n                كلمة المرور\n              </label>\n              <Input\n                id=\"password\"\n                type=\"password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                required\n                className=\"w-full text-left\"\n                placeholder=\"أدخل كلمة المرور\"\n                dir=\"ltr\"\n              />\n            </div>\n\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"w-full\"\n            >\n              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}\n            </Button>\n\n            <div className=\"text-center\">\n              <button\n                type=\"button\"\n                onClick={() => router.push('/signup')}\n                className=\"text-sm text-blue-600 hover:text-blue-500\"\n              >\n                ليس لديك حساب؟ أنشئ حساباً جديداً\n              </button>\n            </div>\n          </form>\n        </CardContent>\n        </Card>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IACzB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,OAAO;YACtC,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAwH,KAAI;kBACzI,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAe,SAAQ;0CAC9D,cAAA,6LAAC;oCAAK,GAAE;;;;;;;;;;;;;;;;sCAGZ,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;4BAAC,WAAU;;8CACpB,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAmC;;;;;;8CAGxD,6LAAC,mIAAA,CAAA,kBAAe;oCAAC,WAAU;8CAAgB;;;;;;;;;;;;sCAI/C,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAK,UAAU;gCAAc,WAAU;;oCACrC,uBACC,6LAAC;wCAAI,WAAU;kDACZ;;;;;;kDAIL,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAAqD;;;;;;0DAGtF,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,KAAI;;;;;;;;;;;;kDAIR,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAqD;;;;;;0DAGzF,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,QAAQ;gDACR,WAAU;gDACV,aAAY;gDACZ,KAAI;;;;;;;;;;;;kDAIR,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;kDAET,UAAU,yBAAyB;;;;;;kDAGtC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf;GAjHgB;;QAKK,6IAAA,CAAA,UAAO;QACX,qIAAA,CAAA,YAAS;;;KANV", "debugId": null}}, {"offset": {"line": 519, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/components/auth/AuthProvider'\nimport { LoginForm } from '@/components/auth/LoginForm'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function Home() {\n  const { user, loading, error } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (user && !loading) {\n      router.push('/dashboard')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-lg text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center max-w-md mx-auto p-6\">\n          <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n            <p className=\"font-bold\">خطأ في الاتصال</p>\n            <p className=\"text-sm\">{error}</p>\n          </div>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            إعادة المحاولة\n          </button>\n        </div>\n      </div>\n    )\n  }\n\n  if (user) {\n    return null // Will redirect to dashboard\n  }\n\n  return <LoginForm />\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,QAAQ,CAAC,SAAS;gBACpB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAY;;;;;;0CACzB,6LAAC;gCAAE,WAAU;0CAAW;;;;;;;;;;;;kCAE1B,6LAAC;wBACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;wBACrC,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,IAAI,MAAM;QACR,OAAO,KAAK,6BAA6B;;IAC3C;IAEA,qBAAO,6LAAC,0IAAA,CAAA,YAAS;;;;;AACnB;GA7CwB;;QACW,6IAAA,CAAA,UAAO;QACzB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}