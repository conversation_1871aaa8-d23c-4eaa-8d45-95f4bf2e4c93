@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* RTL and Arabic Support */
html {
  direction: rtl;
  font-family: 'Cairo', system-ui, sans-serif;
}

body {
  direction: rtl;
  font-family: 'Cairo', system-ui, sans-serif;
  background-color: #f9fafb;
  color: #111827;
}

/* RTL Layout Fixes */
[dir="rtl"] {
  text-align: right;
}

/* Arabic number formatting */
.ltr-numbers {
  direction: ltr;
  text-align: left;
  display: inline-block;
}

/* Fix button text alignment */
button {
  text-align: center;
}

/* Fix input text direction */
input[type="email"],
input[type="password"],
input[type="text"],
input[type="number"] {
  direction: ltr;
  text-align: left;
}

/* Fix label alignment */
label {
  text-align: right;
  display: block;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
