{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name: string\n  role: 'admin' | 'accountant' | 'sales' | 'inventory' | 'viewer'\n  created_at: string\n  updated_at: string\n}\n\nexport interface Account {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'\n  parent_id?: string\n  level: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  email?: string\n  phone?: string\n  address?: string\n  tax_number?: string\n  credit_limit: number\n  balance: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Supplier {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  email?: string\n  phone?: string\n  address?: string\n  tax_number?: string\n  balance: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Product {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  description?: string\n  unit: string\n  cost_price: number\n  selling_price: number\n  stock_quantity: number\n  min_stock_level: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoice {\n  id: string\n  invoice_number: string\n  customer_id: string\n  invoice_date: string\n  due_date: string\n  subtotal: number\n  tax_amount: number\n  discount_amount: number\n  total_amount: number\n  paid_amount: number\n  status: 'draft' | 'confirmed' | 'paid' | 'cancelled'\n  notes?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoiceItem {\n  id: string\n  invoice_id: string\n  product_id: string\n  quantity: number\n  unit_price: number\n  discount_amount: number\n  total_amount: number\n}\n\nexport interface PurchaseInvoice {\n  id: string\n  invoice_number: string\n  supplier_id: string\n  invoice_date: string\n  due_date: string\n  subtotal: number\n  tax_amount: number\n  discount_amount: number\n  total_amount: number\n  paid_amount: number\n  status: 'draft' | 'confirmed' | 'paid' | 'cancelled'\n  notes?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PurchaseInvoiceItem {\n  id: string\n  invoice_id: string\n  product_id: string\n  quantity: number\n  unit_price: number\n  discount_amount: number\n  total_amount: number\n}\n\nexport interface JournalEntry {\n  id: string\n  entry_number: string\n  entry_date: string\n  description: string\n  reference?: string\n  total_debit: number\n  total_credit: number\n  status: 'draft' | 'posted'\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface JournalEntryLine {\n  id: string\n  entry_id: string\n  account_id: string\n  description: string\n  debit_amount: number\n  credit_amount: number\n}\n"], "names": [], "mappings": ";;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 25, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/auth/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  userProfile: any | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<any>\n  signUp: (email: string, password: string, fullName: string) => Promise<any>\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [userProfile, setUserProfile] = useState<any | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial session\n    supabase.auth.getSession().then(({ data: { session } }) => {\n      setUser(session?.user ?? null)\n      if (session?.user) {\n        fetchUserProfile(session.user.id)\n      }\n      setLoading(false)\n    })\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      setUser(session?.user ?? null)\n      if (session?.user) {\n        await fetchUserProfile(session.user.id)\n      } else {\n        setUserProfile(null)\n      }\n      setLoading(false)\n    })\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching user profile:', error)\n        return\n      }\n\n      setUserProfile(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { data, error }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n\n    if (data.user && !error) {\n      // Create user profile\n      const { error: profileError } = await supabase\n        .from('users')\n        .insert([\n          {\n            id: data.user.id,\n            email: data.user.email,\n            full_name: fullName,\n            role: 'viewer', // Default role\n          },\n        ])\n\n      if (profileError) {\n        console.error('Error creating user profile:', profileError)\n      }\n    }\n\n    return { data, error }\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const value = {\n    user,\n    userProfile,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAeA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI;0CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE;oBACpD,QAAQ,SAAS,QAAQ;oBACzB,IAAI,SAAS,MAAM;wBACjB,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBAClC;oBACA,WAAW;gBACb;;YAEA,0BAA0B;YAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAAC,OAAO,OAAO;oBAChD,QAAQ,SAAS,QAAQ;oBACzB,IAAI,SAAS,MAAM;wBACjB,MAAM,iBAAiB,QAAQ,IAAI,CAAC,EAAE;oBACxC,OAAO;wBACL,eAAe;oBACjB;oBACA,WAAW;gBACb;;YAEA;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;QACF;QAEA,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO;YACvB,sBAAsB;YACtB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,SACL,MAAM,CAAC;gBACN;oBACE,IAAI,KAAK,IAAI,CAAC,EAAE;oBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;oBACtB,WAAW;oBACX,MAAM;gBACR;aACD;YAEH,IAAI,cAAc;gBAChB,QAAQ,KAAK,CAAC,gCAAgC;YAChD;QACF;QAEA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAtGgB;KAAA;AAwGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}