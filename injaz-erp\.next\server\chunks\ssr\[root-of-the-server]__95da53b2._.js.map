{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name: string\n  role: 'admin' | 'accountant' | 'sales' | 'inventory' | 'viewer'\n  created_at: string\n  updated_at: string\n}\n\nexport interface Account {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  type: 'asset' | 'liability' | 'equity' | 'revenue' | 'expense'\n  parent_id?: string\n  level: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Customer {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  email?: string\n  phone?: string\n  address?: string\n  tax_number?: string\n  credit_limit: number\n  balance: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Supplier {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  email?: string\n  phone?: string\n  address?: string\n  tax_number?: string\n  balance: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface Product {\n  id: string\n  code: string\n  name: string\n  name_ar: string\n  description?: string\n  unit: string\n  cost_price: number\n  selling_price: number\n  stock_quantity: number\n  min_stock_level: number\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoice {\n  id: string\n  invoice_number: string\n  customer_id: string\n  invoice_date: string\n  due_date: string\n  subtotal: number\n  tax_amount: number\n  discount_amount: number\n  total_amount: number\n  paid_amount: number\n  status: 'draft' | 'confirmed' | 'paid' | 'cancelled'\n  notes?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface SalesInvoiceItem {\n  id: string\n  invoice_id: string\n  product_id: string\n  quantity: number\n  unit_price: number\n  discount_amount: number\n  total_amount: number\n}\n\nexport interface PurchaseInvoice {\n  id: string\n  invoice_number: string\n  supplier_id: string\n  invoice_date: string\n  due_date: string\n  subtotal: number\n  tax_amount: number\n  discount_amount: number\n  total_amount: number\n  paid_amount: number\n  status: 'draft' | 'confirmed' | 'paid' | 'cancelled'\n  notes?: string\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface PurchaseInvoiceItem {\n  id: string\n  invoice_id: string\n  product_id: string\n  quantity: number\n  unit_price: number\n  discount_amount: number\n  total_amount: number\n}\n\nexport interface JournalEntry {\n  id: string\n  entry_number: string\n  entry_date: string\n  description: string\n  reference?: string\n  total_debit: number\n  total_credit: number\n  status: 'draft' | 'posted'\n  created_by: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface JournalEntryLine {\n  id: string\n  entry_id: string\n  account_id: string\n  description: string\n  debit_amount: number\n  credit_amount: number\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/auth/AuthProvider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  userProfile: any | null\n  loading: boolean\n  error: string | null\n  signIn: (email: string, password: string) => Promise<any>\n  signUp: (email: string, password: string, fullName: string) => Promise<any>\n  signOut: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [userProfile, setUserProfile] = useState<any | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    let mounted = true\n\n    // Get initial session\n    const initializeAuth = async () => {\n      try {\n        const { data: { session }, error } = await supabase.auth.getSession()\n\n        if (error) {\n          console.error('Error getting session:', error)\n          // Don't show error for initial load, just set no user\n          if (mounted) {\n            setUser(null)\n            setLoading(false)\n          }\n          return\n        }\n\n        if (mounted) {\n          setUser(session?.user ?? null)\n          if (session?.user) {\n            // Create a basic profile without database dependency\n            setUserProfile({\n              id: session.user.id,\n              email: session.user.email,\n              full_name: session.user.email?.split('@')[0] || 'مستخدم',\n              role: 'admin' // Default role for now\n            })\n          }\n          setLoading(false)\n        }\n      } catch (err) {\n        console.error('Auth initialization error:', err)\n        if (mounted) {\n          // Don't show error for initial load, just set no user\n          setUser(null)\n          setLoading(false)\n        }\n      }\n    }\n\n    initializeAuth()\n\n    // Listen for auth changes\n    const {\n      data: { subscription },\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\n      if (mounted) {\n        setUser(session?.user ?? null)\n        if (session?.user) {\n          // Create a basic profile without database dependency\n          setUserProfile({\n            id: session.user.id,\n            email: session.user.email,\n            full_name: session.user.email?.split('@')[0] || 'مستخدم',\n            role: 'admin' // Default role for now\n          })\n        } else {\n          setUserProfile(null)\n        }\n        setLoading(false)\n      }\n    })\n\n    return () => {\n      mounted = false\n      subscription.unsubscribe()\n    }\n  }, [])\n\n  const fetchUserProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        // If user profile doesn't exist, create a basic one\n        if (error.code === 'PGRST116') {\n          console.log('User profile not found, will create basic profile')\n          setUserProfile({\n            id: userId,\n            full_name: 'مستخدم جديد',\n            role: 'viewer'\n          })\n        } else {\n          console.error('Error fetching user profile:', error)\n        }\n        return\n      }\n\n      setUserProfile(data)\n    } catch (error) {\n      console.error('Error fetching user profile:', error)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    const { data, error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { data, error }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { data, error } = await supabase.auth.signUp({\n      email,\n      password,\n    })\n\n    // For now, we'll skip creating the database profile\n    // This can be added later when the database is properly set up\n\n    return { data, error }\n  }\n\n  const signOut = async () => {\n    const { error } = await supabase.auth.signOut()\n    if (error) {\n      console.error('Error signing out:', error)\n    }\n  }\n\n  const value = {\n    user,\n    userProfile,\n    loading,\n    error,\n    signIn,\n    signUp,\n    signOut,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,sBAAsB;QACtB,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,0BAA0B;oBACxC,sDAAsD;oBACtD,IAAI,SAAS;wBACX,QAAQ;wBACR,WAAW;oBACb;oBACA;gBACF;gBAEA,IAAI,SAAS;oBACX,QAAQ,SAAS,QAAQ;oBACzB,IAAI,SAAS,MAAM;wBACjB,qDAAqD;wBACrD,eAAe;4BACb,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,WAAW,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;4BAChD,MAAM,QAAQ,uBAAuB;wBACvC;oBACF;oBACA,WAAW;gBACb;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI,SAAS;oBACX,sDAAsD;oBACtD,QAAQ;oBACR,WAAW;gBACb;YACF;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,IAAI,SAAS;gBACX,QAAQ,SAAS,QAAQ;gBACzB,IAAI,SAAS,MAAM;oBACjB,qDAAqD;oBACrD,eAAe;wBACb,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;wBACzB,WAAW,QAAQ,IAAI,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;wBAChD,MAAM,QAAQ,uBAAuB;oBACvC;gBACF,OAAO;oBACL,eAAe;gBACjB;gBACA,WAAW;YACb;QACF;QAEA,OAAO;YACL,UAAU;YACV,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,oDAAoD;gBACpD,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,QAAQ,GAAG,CAAC;oBACZ,eAAe;wBACb,IAAI;wBACJ,WAAW;wBACX,MAAM;oBACR;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,gCAAgC;gBAChD;gBACA;YACF;YAEA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;YAC7D;YACA;QACF;QACA,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD;YACA;QACF;QAEA,oDAAoD;QACpD,+DAA+D;QAE/D,OAAO;YAAE;YAAM;QAAM;IACvB;IAEA,MAAM,UAAU;QACd,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;QAC7C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}]}