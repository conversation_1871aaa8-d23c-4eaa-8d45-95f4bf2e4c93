'use client'

import { useAuth } from '@/components/auth/AuthProvider'
import { useRouter } from 'next/navigation'
import { useEffect } from 'react'
import { DashboardLayout } from '@/components/layout/DashboardLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'
import { 
  Users, 
  ShoppingCart, 
  Package, 
  TrendingUp, 
  DollarSign,
  FileText,
  AlertTriangle,
  BarChart3
} from 'lucide-react'

export default function Dashboard() {
  const { user, userProfile, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!user && !loading) {
      router.push('/')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-lg text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-8 rounded-2xl shadow-xl mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-3">
                مرحباً، {userProfile?.full_name || user.email?.split('@')[0] || 'مستخدم'}
              </h1>
              <p className="text-blue-100 text-xl mb-4">
                مرحباً بك في نظام إنجاز ERP - نظام تخطيط موارد المؤسسة
              </p>
              <div className="flex items-center space-x-4 space-x-reverse">
                <div className="bg-white/20 px-4 py-2 rounded-lg">
                  <span className="text-sm">الدور: {userProfile?.role || 'مدير'}</span>
                </div>
                <div className="bg-white/20 px-4 py-2 rounded-lg">
                  <span className="text-sm">آخر دخول: اليوم</span>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="bg-white/10 p-6 rounded-xl">
                <svg className="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-600 text-sm font-medium mb-1">إجمالي المبيعات</p>
                  <p className="text-3xl font-bold text-blue-900 ltr-numbers">245,231 ر.س</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600 font-medium">+20.1%</span>
                    <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
                  </div>
                </div>
                <div className="bg-blue-500 p-3 rounded-xl">
                  <DollarSign className="h-8 w-8 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-600 text-sm font-medium mb-1">عدد العملاء</p>
                  <p className="text-3xl font-bold text-green-900 ltr-numbers">1,234</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600 font-medium">+5.2%</span>
                    <span className="text-sm text-gray-500 mr-2">من الشهر الماضي</span>
                  </div>
                </div>
                <div className="bg-green-500 p-3 rounded-xl">
                  <Users className="h-8 w-8 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-600 text-sm font-medium mb-1">الطلبات</p>
                  <p className="text-3xl font-bold text-purple-900 ltr-numbers">567</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600 font-medium">+12.5%</span>
                    <span className="text-sm text-gray-500 mr-2">من الأسبوع الماضي</span>
                  </div>
                </div>
                <div className="bg-purple-500 p-3 rounded-xl">
                  <ShoppingCart className="h-8 w-8 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-600 text-sm font-medium mb-1">المنتجات</p>
                  <p className="text-3xl font-bold text-orange-900 ltr-numbers">89</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className="h-4 w-4 text-green-500 ml-1" />
                    <span className="text-sm text-green-600 font-medium">+8</span>
                    <span className="text-sm text-gray-500 mr-2">منتجات جديدة</span>
                  </div>
                </div>
                <div className="bg-orange-500 p-3 rounded-xl">
                  <Package className="h-8 w-8 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">إجراءات سريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-blue-50 to-blue-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-blue-500 p-4 rounded-xl">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">فاتورة مبيعات جديدة</h3>
                    <p className="text-gray-600 text-sm mb-4">إنشاء فاتورة مبيعات جديدة للعملاء</p>
                    <Button size="sm" className="w-full">
                      إنشاء فاتورة
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-green-50 to-green-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-green-500 p-4 rounded-xl">
                    <ShoppingCart className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">فاتورة مشتريات جديدة</h3>
                    <p className="text-gray-600 text-sm mb-4">إنشاء فاتورة مشتريات جديدة من الموردين</p>
                    <Button size="sm" className="w-full bg-green-600 hover:bg-green-700">
                      إنشاء فاتورة
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-purple-50 to-purple-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-purple-500 p-4 rounded-xl">
                    <BarChart3 className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">التقارير المالية</h3>
                    <p className="text-gray-600 text-sm mb-4">عرض التقارير المالية والمحاسبية</p>
                    <Button size="sm" className="w-full bg-purple-600 hover:bg-purple-700">
                      عرض التقارير
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-orange-50 to-orange-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-orange-500 p-4 rounded-xl">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">إضافة عميل جديد</h3>
                    <p className="text-gray-600 text-sm mb-4">إضافة عميل جديد إلى قاعدة البيانات</p>
                    <Button size="sm" className="w-full bg-orange-600 hover:bg-orange-700">
                      إضافة عميل
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-indigo-500 p-4 rounded-xl">
                    <Package className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">إضافة منتج جديد</h3>
                    <p className="text-gray-600 text-sm mb-4">إضافة منتج جديد إلى المخزون</p>
                    <Button size="sm" className="w-full bg-indigo-600 hover:bg-indigo-700">
                      إضافة منتج
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-teal-50 to-teal-100">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="bg-teal-500 p-4 rounded-xl">
                    <AlertTriangle className="h-8 w-8 text-white" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">تنبيهات المخزون</h3>
                    <p className="text-gray-600 text-sm mb-4">عرض تنبيهات المخزون المنخفض</p>
                    <Button size="sm" className="w-full bg-teal-600 hover:bg-teal-700">
                      عرض التنبيهات
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Recent Activities & Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                <div className="bg-blue-100 p-2 rounded-lg ml-3">
                  <BarChart3 className="h-6 w-6 text-blue-600" />
                </div>
                الأنشطة الأخيرة
              </CardTitle>
              <CardDescription>آخر العمليات التي تمت في النظام</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-3 bg-green-50 rounded-xl border border-green-200">
                  <div className="bg-green-500 p-2 rounded-lg">
                    <FileText className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-900">تم إنشاء فاتورة مبيعات جديدة</p>
                    <p className="text-xs text-gray-500">فاتورة رقم #INV-2024-001</p>
                    <p className="text-xs text-green-600 font-medium">منذ 5 دقائق</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-3 bg-blue-50 rounded-xl border border-blue-200">
                  <div className="bg-blue-500 p-2 rounded-lg">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-900">تم إضافة عميل جديد</p>
                    <p className="text-xs text-gray-500">شركة الأمل للتجارة</p>
                    <p className="text-xs text-blue-600 font-medium">منذ 15 دقيقة</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-3 bg-purple-50 rounded-xl border border-purple-200">
                  <div className="bg-purple-500 p-2 rounded-lg">
                    <Package className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-900">تحديث المخزون</p>
                    <p className="text-xs text-gray-500">تم استلام 50 قطعة من المنتج A</p>
                    <p className="text-xs text-purple-600 font-medium">منذ 30 دقيقة</p>
                  </div>
                </div>

                <div className="flex items-center gap-4 p-3 bg-orange-50 rounded-xl border border-orange-200">
                  <div className="bg-orange-500 p-2 rounded-lg">
                    <DollarSign className="h-5 w-5 text-white" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-semibold text-gray-900">تم تحصيل دفعة</p>
                    <p className="text-xs text-gray-500">دفعة بقيمة 15,000 ر.س من العميل أحمد محمد</p>
                    <p className="text-xs text-orange-600 font-medium">منذ ساعة</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg">
            <CardHeader className="pb-4">
              <CardTitle className="flex items-center text-xl font-bold text-gray-900">
                <div className="bg-red-100 p-2 rounded-lg ml-3">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                تنبيهات المخزون
              </CardTitle>
              <CardDescription>المنتجات التي تحتاج إلى إعادة تموين</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 bg-red-50 rounded-xl border border-red-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="bg-red-500 p-2 rounded-lg">
                        <Package className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">لابتوب ديل XPS 13</p>
                        <p className="text-xs text-gray-500">كود المنتج: LAP-001</p>
                      </div>
                    </div>
                    <span className="text-xs bg-red-100 text-red-800 px-3 py-1 rounded-full font-medium">
                      نفد المخزون
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">الكمية المتبقية: <span className="font-bold text-red-600">0</span></span>
                    <Button size="sm" variant="outline" className="text-xs">
                      طلب تموين
                    </Button>
                  </div>
                </div>

                <div className="p-4 bg-orange-50 rounded-xl border border-orange-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="bg-orange-500 p-2 rounded-lg">
                        <Package className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">ماوس لوجيتك MX Master</p>
                        <p className="text-xs text-gray-500">كود المنتج: MOU-002</p>
                      </div>
                    </div>
                    <span className="text-xs bg-orange-100 text-orange-800 px-3 py-1 rounded-full font-medium">
                      مخزون منخفض
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">الكمية المتبقية: <span className="font-bold text-orange-600">5</span></span>
                    <Button size="sm" variant="outline" className="text-xs">
                      طلب تموين
                    </Button>
                  </div>
                </div>

                <div className="p-4 bg-yellow-50 rounded-xl border border-yellow-200">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-3">
                      <div className="bg-yellow-500 p-2 rounded-lg">
                        <Package className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-semibold text-gray-900">كيبورد ميكانيكي</p>
                        <p className="text-xs text-gray-500">كود المنتج: KEY-003</p>
                      </div>
                    </div>
                    <span className="text-xs bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium">
                      يحتاج تموين
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">الكمية المتبقية: <span className="font-bold text-yellow-600">12</span></span>
                    <Button size="sm" variant="outline" className="text-xs">
                      طلب تموين
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
