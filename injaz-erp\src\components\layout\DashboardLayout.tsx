'use client'

import { useState } from 'react'
import { useAuth } from '@/components/auth/AuthProvider'
import { Button } from '@/components/ui/Button'
import { 
  Menu,
  X,
  Home,
  Users,
  ShoppingCart,
  Package,
  FileText,
  BarChart3,
  Settings,
  LogOut,
  Calculator,
  Truck,
  RefreshCw
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

interface DashboardLayoutProps {
  children: React.ReactNode
}

const navigation = [
  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },
  { 
    name: 'المبيعات', 
    icon: ShoppingCart,
    children: [
      { name: 'فواتير المبيعات', href: '/dashboard/sales/invoices' },
      { name: 'عروض الأسعار', href: '/dashboard/sales/quotes' },
      { name: 'أوامر البيع', href: '/dashboard/sales/orders' },
    ]
  },
  { 
    name: 'المشتريات', 
    icon: Truck,
    children: [
      { name: 'فواتير المشتريات', href: '/dashboard/purchases/invoices' },
      { name: 'أوامر الشراء', href: '/dashboard/purchases/orders' },
      { name: 'استلام البضائع', href: '/dashboard/purchases/receipts' },
    ]
  },
  { 
    name: 'المرتجعات', 
    icon: RefreshCw,
    children: [
      { name: 'مرتجعات المبيعات', href: '/dashboard/returns/sales' },
      { name: 'مرتجعات المشتريات', href: '/dashboard/returns/purchases' },
    ]
  },
  { 
    name: 'العملاء والموردين', 
    icon: Users,
    children: [
      { name: 'العملاء', href: '/dashboard/customers' },
      { name: 'الموردين', href: '/dashboard/suppliers' },
    ]
  },
  { 
    name: 'المخزون', 
    icon: Package,
    children: [
      { name: 'الأصناف', href: '/dashboard/inventory/products' },
      { name: 'حركات المخزون', href: '/dashboard/inventory/movements' },
      { name: 'تقارير المخزون', href: '/dashboard/inventory/reports' },
    ]
  },
  { 
    name: 'المحاسبة', 
    icon: Calculator,
    children: [
      { name: 'شجرة الحسابات', href: '/dashboard/accounting/accounts' },
      { name: 'القيود اليومية', href: '/dashboard/accounting/journal' },
      { name: 'دفتر الأستاذ', href: '/dashboard/accounting/ledger' },
      { name: 'الحسابات الختامية', href: '/dashboard/accounting/final-accounts' },
    ]
  },
  { 
    name: 'التقارير', 
    icon: BarChart3,
    children: [
      { name: 'التقارير المالية', href: '/dashboard/reports/financial' },
      { name: 'تقارير المبيعات', href: '/dashboard/reports/sales' },
      { name: 'تقارير المشتريات', href: '/dashboard/reports/purchases' },
      { name: 'تقارير المخزون', href: '/dashboard/reports/inventory' },
    ]
  },
  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },
]

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const { signOut, userProfile } = useAuth()
  const pathname = usePathname()

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(item => item !== itemName)
        : [...prev, itemName]
    )
  }

  const handleSignOut = async () => {
    await signOut()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 right-0 flex w-full max-w-xs flex-col bg-white shadow-xl">
          <div className="flex h-16 items-center justify-between px-4">
            <h2 className="text-lg font-semibold">نظام إنجاز ERP</h2>
            <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(false)}>
              <X className="h-6 w-6" />
            </Button>
          </div>
          <nav className="flex-1 space-y-1 px-4 py-4">
            {navigation.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className="flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
                    >
                      <div className="flex items-center gap-3">
                        <item.icon className="h-5 w-5" />
                        {item.name}
                      </div>
                    </button>
                    {expandedItems.includes(item.name) && (
                      <div className="mr-8 mt-1 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className={`block rounded-md px-3 py-2 text-sm ${
                              pathname === child.href
                                ? 'bg-blue-100 text-blue-700'
                                : 'text-gray-600 hover:bg-gray-100'
                            }`}
                          >
                            {child.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${
                      pathname === item.href
                        ? 'bg-blue-100 text-blue-700'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <item.icon className="h-5 w-5" />
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:flex lg:w-80 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto border-l border-gray-200 bg-gradient-to-b from-gray-50 to-white px-6 shadow-xl">
          <div className="flex h-20 shrink-0 items-center justify-center border-b border-gray-200">
            <div className="text-center">
              <div className="bg-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-2">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                </svg>
              </div>
              <h1 className="text-lg font-bold text-gray-900">نظام إنجاز ERP</h1>
              <p className="text-xs text-gray-500">نظام تخطيط الموارد</p>
            </div>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              <li>
                <ul role="list" className="-mx-2 space-y-1">
                  {navigation.map((item) => (
                    <li key={item.name}>
                      {item.children ? (
                        <div>
                          <button
                            onClick={() => toggleExpanded(item.name)}
                            className="flex w-full items-center justify-between rounded-xl p-3 text-sm font-semibold leading-6 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:shadow-md"
                          >
                            <div className="flex items-center gap-x-3">
                              <item.icon className="h-5 w-5 shrink-0" />
                              <span>{item.name}</span>
                            </div>
                            <div className={`transition-transform duration-200 ${expandedItems.includes(item.name) ? 'rotate-90' : ''}`}>
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                              </svg>
                            </div>
                          </button>
                          {expandedItems.includes(item.name) && (
                            <ul className="mr-8 mt-2 space-y-1">
                              {item.children.map((child) => (
                                <li key={child.href}>
                                  <Link
                                    href={child.href}
                                    className={`block rounded-lg py-2 px-3 text-sm leading-6 transition-all duration-200 ${
                                      pathname === child.href
                                        ? 'bg-blue-100 text-blue-700 font-medium shadow-sm'
                                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                                    }`}
                                  >
                                    <span className="flex items-center">
                                      <span className="w-2 h-2 bg-gray-400 rounded-full ml-3"></span>
                                      {child.name}
                                    </span>
                                  </Link>
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      ) : (
                        <Link
                          href={item.href}
                          className={`group flex gap-x-3 rounded-xl p-3 text-sm font-semibold leading-6 items-center transition-all duration-200 ${
                            pathname === item.href
                              ? 'bg-blue-600 text-white shadow-lg'
                              : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:shadow-md'
                          }`}
                        >
                          <item.icon className="h-5 w-5 shrink-0" />
                          <span>{item.name}</span>
                        </Link>
                      )}
                    </li>
                  ))}
                </ul>
              </li>
              <li className="mt-auto">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4 border border-blue-200">
                  <div className="flex items-center gap-x-3 mb-3">
                    <div className="h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                      <span className="text-white text-lg font-bold">
                        {userProfile?.full_name?.charAt(0) || 'م'}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-semibold text-gray-900">
                        {userProfile?.full_name || 'مستخدم'}
                      </p>
                      <p className="text-xs text-gray-500">
                        {userProfile?.role || 'مدير'} • متصل الآن
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    onClick={handleSignOut}
                    className="w-full justify-center gap-x-2 text-sm border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300"
                    size="sm"
                  >
                    <LogOut className="h-4 w-4" />
                    تسجيل الخروج
                  </Button>
                </div>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pr-80">
        {/* Top bar */}
        <div className="sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm px-4 shadow-lg sm:gap-x-6 sm:px-6 lg:px-8">
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden hover:bg-blue-50"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>

          <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
            {/* Search Bar */}
            <div className="flex flex-1 items-center">
              <div className="relative w-full max-w-lg">
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="البحث في النظام..."
                  className="w-full rounded-lg border border-gray-300 bg-white py-2 pr-10 pl-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                  dir="rtl"
                />
              </div>
            </div>

            <div className="flex items-center gap-x-4 lg:gap-x-6">
              {/* Notifications */}
              <Button variant="ghost" size="icon" className="relative hover:bg-blue-50">
                <svg className="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25a2.25 2.25 0 0 1-2.25 2.25H7.5a2.25 2.25 0 0 1-2.25-2.25V9.75a6 6 0 0 1 6-6z" />
                </svg>
                <span className="absolute -top-1 -left-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center">3</span>
              </Button>

              <div className="hidden lg:block lg:h-6 lg:w-px lg:bg-gray-300" />

              {/* User Profile */}
              <div className="flex items-center gap-x-3">
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900">
                    {userProfile?.full_name || 'مستخدم'}
                  </p>
                  <p className="text-xs text-gray-500">
                    {userProfile?.role || 'مدير'}
                  </p>
                </div>
                <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg">
                  <span className="text-white text-sm font-bold">
                    {userProfile?.full_name?.charAt(0) || 'م'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="py-10">
          <div className="px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
