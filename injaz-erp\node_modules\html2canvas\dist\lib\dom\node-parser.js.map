{"version": 3, "file": "node-parser.js", "sourceRoot": "", "sources": ["../../../src/dom/node-parser.ts"], "names": [], "mappings": ";;;AACA,yDAA4D;AAC5D,mDAA+C;AAC/C,uFAAkF;AAClF,yFAAoF;AACpF,mFAA8E;AAC9E,wEAAmE;AACnE,wEAAmE;AACnE,uFAAkF;AAClF,gFAA2E;AAC3E,oFAA+E;AAC/E,yFAAoF;AAGpF,IAAM,WAAW,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;AAEzC,IAAM,aAAa,GAAG,UAAC,OAAgB,EAAE,IAAU,EAAE,MAAwB,EAAE,IAAsB;IACjG,KAAK,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,EAAE,QAAQ,SAAA,EAAE,SAAS,EAAE,SAAS,GAAG,QAAQ,EAAE;QAC7E,QAAQ,GAAG,SAAS,CAAC,WAAW,CAAC;QAEjC,IAAI,kBAAU,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YAC3D,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,8BAAa,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;SAC/E;aAAM,IAAI,qBAAa,CAAC,SAAS,CAAC,EAAE;YACjC,IAAI,qBAAa,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,aAAa,EAAE;gBACrD,SAAS,CAAC,aAAa,EAAE,CAAC,OAAO,CAAC,UAAC,SAAS,IAAK,OAAA,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,EAA/C,CAA+C,CAAC,CAAC;aACrG;iBAAM;gBACH,IAAM,SAAS,GAAG,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;gBACtD,IAAI,SAAS,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;oBAC9B,IAAI,0BAA0B,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,EAAE;wBACxD,SAAS,CAAC,KAAK,yCAAuC,CAAC;qBAC1D;yBAAM,IAAI,sBAAsB,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE;wBACjD,SAAS,CAAC,KAAK,oCAAkC,CAAC;qBACrD;oBAED,IAAI,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;wBAC/C,SAAS,CAAC,KAAK,yBAAuB,CAAC;qBAC1C;oBAED,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAChC,SAAS,CAAC,IAAI,CAAC;oBACf,IAAI,SAAS,CAAC,UAAU,EAAE;wBACtB,aAAa,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;qBACjE;yBAAM,IACH,CAAC,yBAAiB,CAAC,SAAS,CAAC;wBAC7B,CAAC,oBAAY,CAAC,SAAS,CAAC;wBACxB,CAAC,uBAAe,CAAC,SAAS,CAAC,EAC7B;wBACE,aAAa,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;qBACtD;iBACJ;aACJ;SACJ;KACJ;AACL,CAAC,CAAC;AAEF,IAAM,eAAe,GAAG,UAAC,OAAgB,EAAE,OAAgB;IACvD,IAAI,sBAAc,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,IAAI,+CAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACtD;IAED,IAAI,uBAAe,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,IAAI,iDAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACvD;IAED,IAAI,oBAAY,CAAC,OAAO,CAAC,EAAE;QACvB,OAAO,IAAI,2CAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACpD;IAED,IAAI,mBAAW,CAAC,OAAO,CAAC,EAAE;QACtB,OAAO,IAAI,yCAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACnD;IAED,IAAI,mBAAW,CAAC,OAAO,CAAC,EAAE;QACtB,OAAO,IAAI,yCAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACnD;IAED,IAAI,sBAAc,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,IAAI,+CAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACtD;IAED,IAAI,uBAAe,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,IAAI,iDAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACvD;IAED,IAAI,yBAAiB,CAAC,OAAO,CAAC,EAAE;QAC5B,OAAO,IAAI,qDAAwB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACzD;IAED,IAAI,uBAAe,CAAC,OAAO,CAAC,EAAE;QAC1B,OAAO,IAAI,iDAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;KACvD;IAED,OAAO,IAAI,oCAAgB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC,CAAC;AAEK,IAAM,SAAS,GAAG,UAAC,OAAgB,EAAE,OAAoB;IAC5D,IAAM,SAAS,GAAG,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACpD,SAAS,CAAC,KAAK,yCAAuC,CAAC;IACvD,aAAa,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;IACtD,OAAO,SAAS,CAAC;AACrB,CAAC,CAAC;AALW,QAAA,SAAS,aAKpB;AAEF,IAAM,0BAA0B,GAAG,UAAC,IAAa,EAAE,SAA2B,EAAE,IAAsB;IAClG,OAAO,CACH,SAAS,CAAC,MAAM,CAAC,sBAAsB,EAAE;QACzC,SAAS,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC;QAC5B,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE;QAChC,CAAC,qBAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CACvD,CAAC;AACN,CAAC,CAAC;AAEF,IAAM,sBAAsB,GAAG,UAAC,MAA4B,IAAc,OAAA,MAAM,CAAC,YAAY,EAAE,IAAI,MAAM,CAAC,UAAU,EAAE,EAA5C,CAA4C,CAAC;AAEhH,IAAM,UAAU,GAAG,UAAC,IAAU,IAAmB,OAAA,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,EAAhC,CAAgC,CAAC;AAA5E,QAAA,UAAU,cAAkE;AAClF,IAAM,aAAa,GAAG,UAAC,IAAU,IAAsB,OAAA,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,EAAnC,CAAmC,CAAC;AAArF,QAAA,aAAa,iBAAwE;AAC3F,IAAM,iBAAiB,GAAG,UAAC,IAAU;IACxC,OAAA,qBAAa,CAAC,IAAI,CAAC,IAAI,OAAQ,IAAoB,CAAC,KAAK,KAAK,WAAW,IAAI,CAAC,wBAAgB,CAAC,IAAI,CAAC;AAApG,CAAoG,CAAC;AAD5F,QAAA,iBAAiB,qBAC2E;AAClG,IAAM,gBAAgB,GAAG,UAAC,OAAgB;IAC7C,OAAA,OAAQ,OAAsB,CAAC,SAAS,KAAK,QAAQ;AAArD,CAAqD,CAAC;AAD7C,QAAA,gBAAgB,oBAC6B;AACnD,IAAM,WAAW,GAAG,UAAC,IAAa,IAA4B,OAAA,IAAI,CAAC,OAAO,KAAK,IAAI,EAArB,CAAqB,CAAC;AAA9E,QAAA,WAAW,eAAmE;AACpF,IAAM,WAAW,GAAG,UAAC,IAAa,IAA+B,OAAA,IAAI,CAAC,OAAO,KAAK,IAAI,EAArB,CAAqB,CAAC;AAAjF,QAAA,WAAW,eAAsE;AACvF,IAAM,cAAc,GAAG,UAAC,IAAa,IAA+B,OAAA,IAAI,CAAC,OAAO,KAAK,OAAO,EAAxB,CAAwB,CAAC;AAAvF,QAAA,cAAc,kBAAyE;AAC7F,IAAM,aAAa,GAAG,UAAC,IAAa,IAA8B,OAAA,IAAI,CAAC,OAAO,KAAK,MAAM,EAAvB,CAAuB,CAAC;AAApF,QAAA,aAAa,iBAAuE;AAC1F,IAAM,YAAY,GAAG,UAAC,IAAa,IAA4B,OAAA,IAAI,CAAC,OAAO,KAAK,KAAK,EAAtB,CAAsB,CAAC;AAAhF,QAAA,YAAY,gBAAoE;AACtF,IAAM,aAAa,GAAG,UAAC,IAAa,IAA8B,OAAA,IAAI,CAAC,OAAO,KAAK,MAAM,EAAvB,CAAuB,CAAC;AAApF,QAAA,aAAa,iBAAuE;AAC1F,IAAM,eAAe,GAAG,UAAC,IAAa,IAAgC,OAAA,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAzB,CAAyB,CAAC;AAA1F,QAAA,eAAe,mBAA2E;AAChG,IAAM,cAAc,GAAG,UAAC,IAAa,IAA+B,OAAA,IAAI,CAAC,OAAO,KAAK,OAAO,EAAxB,CAAwB,CAAC;AAAvF,QAAA,cAAc,kBAAyE;AAC7F,IAAM,cAAc,GAAG,UAAC,IAAa,IAA+B,OAAA,IAAI,CAAC,OAAO,KAAK,KAAK,EAAtB,CAAsB,CAAC;AAArF,QAAA,cAAc,kBAAuE;AAC3F,IAAM,eAAe,GAAG,UAAC,IAAa,IAAgC,OAAA,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAzB,CAAyB,CAAC;AAA1F,QAAA,eAAe,mBAA2E;AAChG,IAAM,cAAc,GAAG,UAAC,IAAa,IAA+B,OAAA,IAAI,CAAC,OAAO,KAAK,OAAO,EAAxB,CAAwB,CAAC;AAAvF,QAAA,cAAc,kBAAyE;AAC7F,IAAM,eAAe,GAAG,UAAC,IAAa,IAAgC,OAAA,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAzB,CAAyB,CAAC;AAA1F,QAAA,eAAe,mBAA2E;AAChG,IAAM,iBAAiB,GAAG,UAAC,IAAa,IAAkC,OAAA,IAAI,CAAC,OAAO,KAAK,UAAU,EAA3B,CAA2B,CAAC;AAAhG,QAAA,iBAAiB,qBAA+E;AACtG,IAAM,eAAe,GAAG,UAAC,IAAa,IAAgC,OAAA,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAzB,CAAyB,CAAC;AAA1F,QAAA,eAAe,mBAA2E;AAChG,IAAM,aAAa,GAAG,UAAC,IAAa,IAA8B,OAAA,IAAI,CAAC,OAAO,KAAK,MAAM,EAAvB,CAAuB,CAAC;AAApF,QAAA,aAAa,iBAAuE;AACjG,wFAAwF;AACjF,IAAM,eAAe,GAAG,UAAC,IAAa,IAA0B,OAAA,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAA7B,CAA6B,CAAC;AAAxF,QAAA,eAAe,mBAAyE"}