{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number): string {\n  return new Intl.NumberFormat('ar-SA', {\n    style: 'currency',\n    currency: 'SAR',\n    minimumFractionDigits: 2,\n  }).format(amount)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('ar-SA', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatNumber(number: number): string {\n  return new Intl.NumberFormat('ar-SA').format(number)\n}\n\nexport function generateInvoiceNumber(prefix: string = 'INV'): string {\n  const timestamp = Date.now().toString().slice(-6)\n  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')\n  return `${prefix}-${timestamp}-${random}`\n}\n\nexport function generateAccountCode(parentCode: string, sequence: number): string {\n  return `${parentCode}${sequence.toString().padStart(2, '0')}`\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc;IAC3C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS,MAAM,CAAC;AAC/C;AAEO,SAAS,sBAAsB,SAAiB,KAAK;IAC1D,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG;IACvE,OAAO,GAAG,OAAO,CAAC,EAAE,UAAU,CAAC,EAAE,QAAQ;AAC3C;AAEO,SAAS,oBAAoB,UAAkB,EAAE,QAAgB;IACtE,OAAO,GAAG,aAAa,SAAS,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;AAC/D", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          \"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-center shadow-sm\",\n          {\n            \"bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md active:bg-blue-800\": variant === 'default',\n            \"bg-red-600 text-white hover:bg-red-700 hover:shadow-md active:bg-red-800\": variant === 'destructive',\n            \"border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400\": variant === 'outline',\n            \"bg-gray-100 text-gray-900 hover:bg-gray-200\": variant === 'secondary',\n            \"hover:bg-gray-100 hover:text-gray-900 text-gray-700\": variant === 'ghost',\n            \"text-blue-600 underline-offset-4 hover:underline bg-transparent shadow-none\": variant === 'link',\n          },\n          {\n            \"h-10 px-4 py-2 text-sm\": size === 'default',\n            \"h-8 px-3 py-1 text-xs\": size === 'sm',\n            \"h-12 px-6 py-3 text-base\": size === 'lg',\n            \"h-10 w-10 p-0\": size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uSACA;YACE,+EAA+E,YAAY;YAC3F,4EAA4E,YAAY;YACxF,wFAAwF,YAAY;YACpG,+CAA+C,YAAY;YAC3D,uDAAuD,YAAY;YACnE,+EAA+E,YAAY;QAC7F,GACA;YACE,0BAA0B,SAAS;YACnC,yBAAyB,SAAS;YAClC,4BAA4B,SAAS;YACrC,iBAAiB,SAAS;QAC5B,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/components/auth/AuthProvider'\nimport { Button } from '@/components/ui/Button'\nimport { \n  Menu,\n  X,\n  Home,\n  Users,\n  ShoppingCart,\n  Package,\n  FileText,\n  BarChart3,\n  Settings,\n  LogOut,\n  Calculator,\n  Truck,\n  RefreshCw\n} from 'lucide-react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nconst navigation = [\n  { name: 'لوحة التحكم', href: '/dashboard', icon: Home },\n  { \n    name: 'المبيعات', \n    icon: ShoppingCart,\n    children: [\n      { name: 'فواتير المبيعات', href: '/dashboard/sales/invoices' },\n      { name: 'عروض الأسعار', href: '/dashboard/sales/quotes' },\n      { name: 'أوامر البيع', href: '/dashboard/sales/orders' },\n    ]\n  },\n  { \n    name: 'المشتريات', \n    icon: Truck,\n    children: [\n      { name: 'فواتير المشتريات', href: '/dashboard/purchases/invoices' },\n      { name: 'أوامر الشراء', href: '/dashboard/purchases/orders' },\n      { name: 'استلام البضائع', href: '/dashboard/purchases/receipts' },\n    ]\n  },\n  { \n    name: 'المرتجعات', \n    icon: RefreshCw,\n    children: [\n      { name: 'مرتجعات المبيعات', href: '/dashboard/returns/sales' },\n      { name: 'مرتجعات المشتريات', href: '/dashboard/returns/purchases' },\n    ]\n  },\n  { \n    name: 'العملاء والموردين', \n    icon: Users,\n    children: [\n      { name: 'العملاء', href: '/dashboard/customers' },\n      { name: 'الموردين', href: '/dashboard/suppliers' },\n    ]\n  },\n  { \n    name: 'المخزون', \n    icon: Package,\n    children: [\n      { name: 'الأصناف', href: '/dashboard/inventory/products' },\n      { name: 'حركات المخزون', href: '/dashboard/inventory/movements' },\n      { name: 'تقارير المخزون', href: '/dashboard/inventory/reports' },\n    ]\n  },\n  { \n    name: 'المحاسبة', \n    icon: Calculator,\n    children: [\n      { name: 'شجرة الحسابات', href: '/dashboard/accounting/accounts' },\n      { name: 'القيود اليومية', href: '/dashboard/accounting/journal' },\n      { name: 'دفتر الأستاذ', href: '/dashboard/accounting/ledger' },\n      { name: 'الحسابات الختامية', href: '/dashboard/accounting/final-accounts' },\n    ]\n  },\n  { \n    name: 'التقارير', \n    icon: BarChart3,\n    children: [\n      { name: 'التقارير المالية', href: '/dashboard/reports/financial' },\n      { name: 'تقارير المبيعات', href: '/dashboard/reports/sales' },\n      { name: 'تقارير المشتريات', href: '/dashboard/reports/purchases' },\n      { name: 'تقارير المخزون', href: '/dashboard/reports/inventory' },\n    ]\n  },\n  { name: 'الإعدادات', href: '/dashboard/settings', icon: Settings },\n]\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\n  const { signOut, userProfile } = useAuth()\n  const pathname = usePathname()\n\n  const toggleExpanded = (itemName: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemName) \n        ? prev.filter(item => item !== itemName)\n        : [...prev, itemName]\n    )\n  }\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar */}\n      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>\n        <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" onClick={() => setSidebarOpen(false)} />\n        <div className=\"fixed inset-y-0 right-0 flex w-full max-w-xs flex-col bg-white shadow-xl\">\n          <div className=\"flex h-16 items-center justify-between px-4\">\n            <h2 className=\"text-lg font-semibold\">نظام إنجاز ERP</h2>\n            <Button variant=\"ghost\" size=\"icon\" onClick={() => setSidebarOpen(false)}>\n              <X className=\"h-6 w-6\" />\n            </Button>\n          </div>\n          <nav className=\"flex-1 space-y-1 px-4 py-4\">\n            {navigation.map((item) => (\n              <div key={item.name}>\n                {item.children ? (\n                  <div>\n                    <button\n                      onClick={() => toggleExpanded(item.name)}\n                      className=\"flex w-full items-center justify-between rounded-md px-3 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100\"\n                    >\n                      <div className=\"flex items-center gap-3\">\n                        <item.icon className=\"h-5 w-5\" />\n                        {item.name}\n                      </div>\n                    </button>\n                    {expandedItems.includes(item.name) && (\n                      <div className=\"mr-8 mt-1 space-y-1\">\n                        {item.children.map((child) => (\n                          <Link\n                            key={child.href}\n                            href={child.href}\n                            className={`block rounded-md px-3 py-2 text-sm ${\n                              pathname === child.href\n                                ? 'bg-blue-100 text-blue-700'\n                                : 'text-gray-600 hover:bg-gray-100'\n                            }`}\n                          >\n                            {child.name}\n                          </Link>\n                        ))}\n                      </div>\n                    )}\n                  </div>\n                ) : (\n                  <Link\n                    href={item.href}\n                    className={`flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium ${\n                      pathname === item.href\n                        ? 'bg-blue-100 text-blue-700'\n                        : 'text-gray-700 hover:bg-gray-100'\n                    }`}\n                  >\n                    <item.icon className=\"h-5 w-5\" />\n                    {item.name}\n                  </Link>\n                )}\n              </div>\n            ))}\n          </nav>\n        </div>\n      </div>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden lg:fixed lg:inset-y-0 lg:right-0 lg:z-50 lg:flex lg:w-80 lg:flex-col\">\n        <div className=\"flex grow flex-col gap-y-5 overflow-y-auto border-l border-gray-200 bg-gradient-to-b from-gray-50 to-white px-6 shadow-xl\">\n          <div className=\"flex h-20 shrink-0 items-center justify-center border-b border-gray-200\">\n            <div className=\"text-center\">\n              <div className=\"bg-blue-600 w-12 h-12 rounded-xl flex items-center justify-center mx-auto mb-2\">\n                <svg className=\"w-6 h-6 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n                </svg>\n              </div>\n              <h1 className=\"text-lg font-bold text-gray-900\">نظام إنجاز ERP</h1>\n              <p className=\"text-xs text-gray-500\">نظام تخطيط الموارد</p>\n            </div>\n          </div>\n          <nav className=\"flex flex-1 flex-col\">\n            <ul role=\"list\" className=\"flex flex-1 flex-col gap-y-7\">\n              <li>\n                <ul role=\"list\" className=\"-mx-2 space-y-1\">\n                  {navigation.map((item) => (\n                    <li key={item.name}>\n                      {item.children ? (\n                        <div>\n                          <button\n                            onClick={() => toggleExpanded(item.name)}\n                            className=\"flex w-full items-center justify-between rounded-xl p-3 text-sm font-semibold leading-6 text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 hover:shadow-md\"\n                          >\n                            <div className=\"flex items-center gap-x-3\">\n                              <item.icon className=\"h-5 w-5 shrink-0\" />\n                              <span>{item.name}</span>\n                            </div>\n                            <div className={`transition-transform duration-200 ${expandedItems.includes(item.name) ? 'rotate-90' : ''}`}>\n                              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                              </svg>\n                            </div>\n                          </button>\n                          {expandedItems.includes(item.name) && (\n                            <ul className=\"mr-8 mt-2 space-y-1\">\n                              {item.children.map((child) => (\n                                <li key={child.href}>\n                                  <Link\n                                    href={child.href}\n                                    className={`block rounded-lg py-2 px-3 text-sm leading-6 transition-all duration-200 ${\n                                      pathname === child.href\n                                        ? 'bg-blue-100 text-blue-700 font-medium shadow-sm'\n                                        : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'\n                                    }`}\n                                  >\n                                    <span className=\"flex items-center\">\n                                      <span className=\"w-2 h-2 bg-gray-400 rounded-full ml-3\"></span>\n                                      {child.name}\n                                    </span>\n                                  </Link>\n                                </li>\n                              ))}\n                            </ul>\n                          )}\n                        </div>\n                      ) : (\n                        <Link\n                          href={item.href}\n                          className={`group flex gap-x-3 rounded-xl p-3 text-sm font-semibold leading-6 items-center transition-all duration-200 ${\n                            pathname === item.href\n                              ? 'bg-blue-600 text-white shadow-lg'\n                              : 'text-gray-700 hover:bg-blue-50 hover:text-blue-600 hover:shadow-md'\n                          }`}\n                        >\n                          <item.icon className=\"h-5 w-5 shrink-0\" />\n                          <span>{item.name}</span>\n                        </Link>\n                      )}\n                    </li>\n                  ))}\n                </ul>\n              </li>\n              <li className=\"mt-auto\">\n                <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-4 border border-blue-200\">\n                  <div className=\"flex items-center gap-x-3 mb-3\">\n                    <div className=\"h-12 w-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg\">\n                      <span className=\"text-white text-lg font-bold\">\n                        {userProfile?.full_name?.charAt(0) || 'م'}\n                      </span>\n                    </div>\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-semibold text-gray-900\">\n                        {userProfile?.full_name || 'مستخدم'}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {userProfile?.role || 'مدير'} • متصل الآن\n                      </p>\n                    </div>\n                  </div>\n                  <Button\n                    variant=\"outline\"\n                    onClick={handleSignOut}\n                    className=\"w-full justify-center gap-x-2 text-sm border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300\"\n                    size=\"sm\"\n                  >\n                    <LogOut className=\"h-4 w-4\" />\n                    تسجيل الخروج\n                  </Button>\n                </div>\n              </li>\n            </ul>\n          </nav>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pr-80\">\n        {/* Top bar */}\n        <div className=\"sticky top-0 z-40 flex h-20 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm px-4 shadow-lg sm:gap-x-6 sm:px-6 lg:px-8\">\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"lg:hidden hover:bg-blue-50\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </Button>\n\n          <div className=\"flex flex-1 gap-x-4 self-stretch lg:gap-x-6\">\n            {/* Search Bar */}\n            <div className=\"flex flex-1 items-center\">\n              <div className=\"relative w-full max-w-lg\">\n                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n                <input\n                  type=\"text\"\n                  placeholder=\"البحث في النظام...\"\n                  className=\"w-full rounded-lg border border-gray-300 bg-white py-2 pr-10 pl-4 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500\"\n                  dir=\"rtl\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center gap-x-4 lg:gap-x-6\">\n              {/* Notifications */}\n              <Button variant=\"ghost\" size=\"icon\" className=\"relative hover:bg-blue-50\">\n                <svg className=\"h-6 w-6 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25a2.25 2.25 0 0 1-2.25 2.25H7.5a2.25 2.25 0 0 1-2.25-2.25V9.75a6 6 0 0 1 6-6z\" />\n                </svg>\n                <span className=\"absolute -top-1 -left-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center\">3</span>\n              </Button>\n\n              <div className=\"hidden lg:block lg:h-6 lg:w-px lg:bg-gray-300\" />\n\n              {/* User Profile */}\n              <div className=\"flex items-center gap-x-3\">\n                <div className=\"text-right\">\n                  <p className=\"text-sm font-semibold text-gray-900\">\n                    {userProfile?.full_name || 'مستخدم'}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">\n                    {userProfile?.role || 'مدير'}\n                  </p>\n                </div>\n                <div className=\"h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center shadow-lg\">\n                  <span className=\"text-white text-sm font-bold\">\n                    {userProfile?.full_name?.charAt(0) || 'م'}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page content */}\n        <main className=\"py-10\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AACA;;;AArBA;;;;;;;AA2BA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAe,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IACtD;QACE,MAAM;QACN,MAAM,yNAAA,CAAA,eAAY;QAClB,UAAU;YACR;gBAAE,MAAM;gBAAmB,MAAM;YAA4B;YAC7D;gBAAE,MAAM;gBAAgB,MAAM;YAA0B;YACxD;gBAAE,MAAM;gBAAe,MAAM;YAA0B;SACxD;IACH;IACA;QACE,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAAgC;YAClE;gBAAE,MAAM;gBAAgB,MAAM;YAA8B;YAC5D;gBAAE,MAAM;gBAAkB,MAAM;YAAgC;SACjE;IACH;IACA;QACE,MAAM;QACN,MAAM,mNAAA,CAAA,YAAS;QACf,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAA2B;YAC7D;gBAAE,MAAM;gBAAqB,MAAM;YAA+B;SACnE;IACH;IACA;QACE,MAAM;QACN,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAuB;YAChD;gBAAE,MAAM;gBAAY,MAAM;YAAuB;SAClD;IACH;IACA;QACE,MAAM;QACN,MAAM,2MAAA,CAAA,UAAO;QACb,UAAU;YACR;gBAAE,MAAM;gBAAW,MAAM;YAAgC;YACzD;gBAAE,MAAM;gBAAiB,MAAM;YAAiC;YAChE;gBAAE,MAAM;gBAAkB,MAAM;YAA+B;SAChE;IACH;IACA;QACE,MAAM;QACN,MAAM,iNAAA,CAAA,aAAU;QAChB,UAAU;YACR;gBAAE,MAAM;gBAAiB,MAAM;YAAiC;YAChE;gBAAE,MAAM;gBAAkB,MAAM;YAAgC;YAChE;gBAAE,MAAM;gBAAgB,MAAM;YAA+B;YAC7D;gBAAE,MAAM;gBAAqB,MAAM;YAAuC;SAC3E;IACH;IACA;QACE,MAAM;QACN,MAAM,qNAAA,CAAA,YAAS;QACf,UAAU;YACR;gBAAE,MAAM;gBAAoB,MAAM;YAA+B;YACjE;gBAAE,MAAM;gBAAmB,MAAM;YAA2B;YAC5D;gBAAE,MAAM;gBAAoB,MAAM;YAA+B;YACjE;gBAAE,MAAM;gBAAkB,MAAM;YAA+B;SAChE;IACH;IACA;QAAE,MAAM;QAAa,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAClE;AAEM,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IACvC,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,YACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,YAC7B;mBAAI;gBAAM;aAAS;IAE3B;IAEA,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAW,CAAC,6BAA6B,EAAE,cAAc,UAAU,UAAU;;kCAChF,6LAAC;wBAAI,WAAU;wBAA0C,SAAS,IAAM,eAAe;;;;;;kCACvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,SAAS,IAAM,eAAe;kDAChE,cAAA,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;kDACE,KAAK,QAAQ,iBACZ,6LAAC;;8DACC,6LAAC;oDACC,SAAS,IAAM,eAAe,KAAK,IAAI;oDACvC,WAAU;8DAEV,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;4DACpB,KAAK,IAAI;;;;;;;;;;;;gDAGb,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,6LAAC;oDAAI,WAAU;8DACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,6LAAC,+JAAA,CAAA,UAAI;4DAEH,MAAM,MAAM,IAAI;4DAChB,WAAW,CAAC,mCAAmC,EAC7C,aAAa,MAAM,IAAI,GACnB,8BACA,mCACJ;sEAED,MAAM,IAAI;2DARN,MAAM,IAAI;;;;;;;;;;;;;;;iEAezB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,CAAC,iEAAiE,EAC3E,aAAa,KAAK,IAAI,GAClB,8BACA,mCACJ;;8DAEF,6LAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;gDACpB,KAAK,IAAI;;;;;;;uCAxCN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;0BAkD3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAqB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC5E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;sCAGzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,MAAK;gCAAO,WAAU;;kDACxB,6LAAC;kDACC,cAAA,6LAAC;4CAAG,MAAK;4CAAO,WAAU;sDACvB,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;8DACE,KAAK,QAAQ,iBACZ,6LAAC;;0EACC,6LAAC;gEACC,SAAS,IAAM,eAAe,KAAK,IAAI;gEACvC,WAAU;;kFAEV,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,KAAK,IAAI;gFAAC,WAAU;;;;;;0FACrB,6LAAC;0FAAM,KAAK,IAAI;;;;;;;;;;;;kFAElB,6LAAC;wEAAI,WAAW,CAAC,kCAAkC,EAAE,cAAc,QAAQ,CAAC,KAAK,IAAI,IAAI,cAAc,IAAI;kFACzG,cAAA,6LAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;4DAI1E,cAAc,QAAQ,CAAC,KAAK,IAAI,mBAC/B,6LAAC;gEAAG,WAAU;0EACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,sBAClB,6LAAC;kFACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4EACH,MAAM,MAAM,IAAI;4EAChB,WAAW,CAAC,yEAAyE,EACnF,aAAa,MAAM,IAAI,GACnB,oDACA,uDACJ;sFAEF,cAAA,6LAAC;gFAAK,WAAU;;kGACd,6LAAC;wFAAK,WAAU;;;;;;oFACf,MAAM,IAAI;;;;;;;;;;;;uEAXR,MAAM,IAAI;;;;;;;;;;;;;;;6EAoB3B,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,KAAK,IAAI;wDACf,WAAW,CAAC,2GAA2G,EACrH,aAAa,KAAK,IAAI,GAClB,qCACA,sEACJ;;0EAEF,6LAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,6LAAC;0EAAM,KAAK,IAAI;;;;;;;;;;;;mDAjDb,KAAK,IAAI;;;;;;;;;;;;;;;kDAwDxB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;0EACb,aAAa,WAAW,OAAO,MAAM;;;;;;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EACV,aAAa,aAAa;;;;;;8EAE7B,6LAAC;oEAAE,WAAU;;wEACV,aAAa,QAAQ;wEAAO;;;;;;;;;;;;;;;;;;;8DAInC,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,SAAS;oDACT,WAAU;oDACV,MAAK;;sEAEL,6LAAC,6MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,KAAI;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAQ,MAAK;gDAAO,WAAU;;kEAC5C,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA8G;;;;;;;;;;;;0DAGhI,6LAAC;gDAAI,WAAU;;;;;;0DAGf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EACV,aAAa,aAAa;;;;;;0EAE7B,6LAAC;gEAAE,WAAU;0EACV,aAAa,QAAQ;;;;;;;;;;;;kEAG1B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;sEACb,aAAa,WAAW,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASlD,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACZ;;;;;;;;;;;;;;;;;;;;;;;AAMb;GApQgB;;QAGmB,6IAAA,CAAA,UAAO;QACvB,qIAAA,CAAA,cAAW;;;KAJd", "debugId": null}}, {"offset": {"line": 997, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/components/ui/Card.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white text-gray-900 shadow-sm hover:shadow-md transition-shadow duration-200\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qHACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/Injaz3/injaz-erp/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/components/auth/AuthProvider'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { DashboardLayout } from '@/components/layout/DashboardLayout'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card'\nimport { \n  Users, \n  ShoppingCart, \n  Package, \n  TrendingUp, \n  DollarSign,\n  FileText,\n  AlertTriangle,\n  BarChart3\n} from 'lucide-react'\n\nexport default function Dashboard() {\n  const { user, userProfile, loading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!user && !loading) {\n      router.push('/')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary mx-auto\"></div>\n          <p className=\"mt-4 text-lg text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null // Will redirect to login\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"space-y-6\">\n        {/* Welcome Section */}\n        <div className=\"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-8 rounded-2xl shadow-xl mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-4xl font-bold mb-3\">\n                مرحباً، {userProfile?.full_name || user.email?.split('@')[0] || 'مستخدم'}\n              </h1>\n              <p className=\"text-blue-100 text-xl mb-4\">\n                مرحباً بك في نظام إنجاز ERP - نظام تخطيط موارد المؤسسة\n              </p>\n              <div className=\"flex items-center space-x-4 space-x-reverse\">\n                <div className=\"bg-white/20 px-4 py-2 rounded-lg\">\n                  <span className=\"text-sm\">الدور: {userProfile?.role || 'مدير'}</span>\n                </div>\n                <div className=\"bg-white/20 px-4 py-2 rounded-lg\">\n                  <span className=\"text-sm\">آخر دخول: اليوم</span>\n                </div>\n              </div>\n            </div>\n            <div className=\"hidden md:block\">\n              <div className=\"bg-white/10 p-6 rounded-xl\">\n                <svg className=\"w-16 h-16 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                </svg>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <Card className=\"bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200 hover:shadow-lg transition-all duration-300\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-blue-600 text-sm font-medium mb-1\">إجمالي المبيعات</p>\n                  <p className=\"text-3xl font-bold text-blue-900 ltr-numbers\">245,231 ر.س</p>\n                  <div className=\"flex items-center mt-2\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 ml-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+20.1%</span>\n                    <span className=\"text-sm text-gray-500 mr-2\">من الشهر الماضي</span>\n                  </div>\n                </div>\n                <div className=\"bg-blue-500 p-3 rounded-xl\">\n                  <DollarSign className=\"h-8 w-8 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-gradient-to-br from-green-50 to-green-100 border-green-200 hover:shadow-lg transition-all duration-300\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-green-600 text-sm font-medium mb-1\">عدد العملاء</p>\n                  <p className=\"text-3xl font-bold text-green-900 ltr-numbers\">1,234</p>\n                  <div className=\"flex items-center mt-2\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 ml-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+5.2%</span>\n                    <span className=\"text-sm text-gray-500 mr-2\">من الشهر الماضي</span>\n                  </div>\n                </div>\n                <div className=\"bg-green-500 p-3 rounded-xl\">\n                  <Users className=\"h-8 w-8 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200 hover:shadow-lg transition-all duration-300\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-purple-600 text-sm font-medium mb-1\">الطلبات</p>\n                  <p className=\"text-3xl font-bold text-purple-900 ltr-numbers\">567</p>\n                  <div className=\"flex items-center mt-2\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 ml-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+12.5%</span>\n                    <span className=\"text-sm text-gray-500 mr-2\">من الأسبوع الماضي</span>\n                  </div>\n                </div>\n                <div className=\"bg-purple-500 p-3 rounded-xl\">\n                  <ShoppingCart className=\"h-8 w-8 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200 hover:shadow-lg transition-all duration-300\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-orange-600 text-sm font-medium mb-1\">المنتجات</p>\n                  <p className=\"text-3xl font-bold text-orange-900 ltr-numbers\">89</p>\n                  <div className=\"flex items-center mt-2\">\n                    <TrendingUp className=\"h-4 w-4 text-green-500 ml-1\" />\n                    <span className=\"text-sm text-green-600 font-medium\">+8</span>\n                    <span className=\"text-sm text-gray-500 mr-2\">منتجات جديدة</span>\n                  </div>\n                </div>\n                <div className=\"bg-orange-500 p-3 rounded-xl\">\n                  <Package className=\"h-8 w-8 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">إجراءات سريعة</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-blue-50 to-blue-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-blue-500 p-4 rounded-xl\">\n                    <FileText className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">فاتورة مبيعات جديدة</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">إنشاء فاتورة مبيعات جديدة للعملاء</p>\n                    <Button size=\"sm\" className=\"w-full\">\n                      إنشاء فاتورة\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-green-50 to-green-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-green-500 p-4 rounded-xl\">\n                    <ShoppingCart className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">فاتورة مشتريات جديدة</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">إنشاء فاتورة مشتريات جديدة من الموردين</p>\n                    <Button size=\"sm\" className=\"w-full bg-green-600 hover:bg-green-700\">\n                      إنشاء فاتورة\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-purple-50 to-purple-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-purple-500 p-4 rounded-xl\">\n                    <BarChart3 className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">التقارير المالية</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">عرض التقارير المالية والمحاسبية</p>\n                    <Button size=\"sm\" className=\"w-full bg-purple-600 hover:bg-purple-700\">\n                      عرض التقارير\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-orange-50 to-orange-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-orange-500 p-4 rounded-xl\">\n                    <Users className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">إضافة عميل جديد</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">إضافة عميل جديد إلى قاعدة البيانات</p>\n                    <Button size=\"sm\" className=\"w-full bg-orange-600 hover:bg-orange-700\">\n                      إضافة عميل\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-indigo-50 to-indigo-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-indigo-500 p-4 rounded-xl\">\n                    <Package className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">إضافة منتج جديد</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">إضافة منتج جديد إلى المخزون</p>\n                    <Button size=\"sm\" className=\"w-full bg-indigo-600 hover:bg-indigo-700\">\n                      إضافة منتج\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-0 bg-gradient-to-br from-teal-50 to-teal-100\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"bg-teal-500 p-4 rounded-xl\">\n                    <AlertTriangle className=\"h-8 w-8 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">تنبيهات المخزون</h3>\n                    <p className=\"text-gray-600 text-sm mb-4\">عرض تنبيهات المخزون المنخفض</p>\n                    <Button size=\"sm\" className=\"w-full bg-teal-600 hover:bg-teal-700\">\n                      عرض التنبيهات\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n\n        {/* Recent Activities & Alerts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          <Card className=\"border-0 shadow-lg\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center text-xl font-bold text-gray-900\">\n                <div className=\"bg-blue-100 p-2 rounded-lg ml-3\">\n                  <BarChart3 className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                الأنشطة الأخيرة\n              </CardTitle>\n              <CardDescription>آخر العمليات التي تمت في النظام</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center gap-4 p-3 bg-green-50 rounded-xl border border-green-200\">\n                  <div className=\"bg-green-500 p-2 rounded-lg\">\n                    <FileText className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-semibold text-gray-900\">تم إنشاء فاتورة مبيعات جديدة</p>\n                    <p className=\"text-xs text-gray-500\">فاتورة رقم #INV-2024-001</p>\n                    <p className=\"text-xs text-green-600 font-medium\">منذ 5 دقائق</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-4 p-3 bg-blue-50 rounded-xl border border-blue-200\">\n                  <div className=\"bg-blue-500 p-2 rounded-lg\">\n                    <Users className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-semibold text-gray-900\">تم إضافة عميل جديد</p>\n                    <p className=\"text-xs text-gray-500\">شركة الأمل للتجارة</p>\n                    <p className=\"text-xs text-blue-600 font-medium\">منذ 15 دقيقة</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-4 p-3 bg-purple-50 rounded-xl border border-purple-200\">\n                  <div className=\"bg-purple-500 p-2 rounded-lg\">\n                    <Package className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-semibold text-gray-900\">تحديث المخزون</p>\n                    <p className=\"text-xs text-gray-500\">تم استلام 50 قطعة من المنتج A</p>\n                    <p className=\"text-xs text-purple-600 font-medium\">منذ 30 دقيقة</p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center gap-4 p-3 bg-orange-50 rounded-xl border border-orange-200\">\n                  <div className=\"bg-orange-500 p-2 rounded-lg\">\n                    <DollarSign className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-semibold text-gray-900\">تم تحصيل دفعة</p>\n                    <p className=\"text-xs text-gray-500\">دفعة بقيمة 15,000 ر.س من العميل أحمد محمد</p>\n                    <p className=\"text-xs text-orange-600 font-medium\">منذ ساعة</p>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 shadow-lg\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center text-xl font-bold text-gray-900\">\n                <div className=\"bg-red-100 p-2 rounded-lg ml-3\">\n                  <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n                </div>\n                تنبيهات المخزون\n              </CardTitle>\n              <CardDescription>المنتجات التي تحتاج إلى إعادة تموين</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div className=\"p-4 bg-red-50 rounded-xl border border-red-200\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"bg-red-500 p-2 rounded-lg\">\n                        <Package className=\"h-4 w-4 text-white\" />\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-semibold text-gray-900\">لابتوب ديل XPS 13</p>\n                        <p className=\"text-xs text-gray-500\">كود المنتج: LAP-001</p>\n                      </div>\n                    </div>\n                    <span className=\"text-xs bg-red-100 text-red-800 px-3 py-1 rounded-full font-medium\">\n                      نفد المخزون\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-xs text-gray-600\">الكمية المتبقية: <span className=\"font-bold text-red-600\">0</span></span>\n                    <Button size=\"sm\" variant=\"outline\" className=\"text-xs\">\n                      طلب تموين\n                    </Button>\n                  </div>\n                </div>\n\n                <div className=\"p-4 bg-orange-50 rounded-xl border border-orange-200\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"bg-orange-500 p-2 rounded-lg\">\n                        <Package className=\"h-4 w-4 text-white\" />\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-semibold text-gray-900\">ماوس لوجيتك MX Master</p>\n                        <p className=\"text-xs text-gray-500\">كود المنتج: MOU-002</p>\n                      </div>\n                    </div>\n                    <span className=\"text-xs bg-orange-100 text-orange-800 px-3 py-1 rounded-full font-medium\">\n                      مخزون منخفض\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-xs text-gray-600\">الكمية المتبقية: <span className=\"font-bold text-orange-600\">5</span></span>\n                    <Button size=\"sm\" variant=\"outline\" className=\"text-xs\">\n                      طلب تموين\n                    </Button>\n                  </div>\n                </div>\n\n                <div className=\"p-4 bg-yellow-50 rounded-xl border border-yellow-200\">\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"bg-yellow-500 p-2 rounded-lg\">\n                        <Package className=\"h-4 w-4 text-white\" />\n                      </div>\n                      <div>\n                        <p className=\"text-sm font-semibold text-gray-900\">كيبورد ميكانيكي</p>\n                        <p className=\"text-xs text-gray-500\">كود المنتج: KEY-003</p>\n                      </div>\n                    </div>\n                    <span className=\"text-xs bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full font-medium\">\n                      يحتاج تموين\n                    </span>\n                  </div>\n                  <div className=\"flex justify-between items-center\">\n                    <span className=\"text-xs text-gray-600\">الكمية المتبقية: <span className=\"font-bold text-yellow-600\">12</span></span>\n                    <Button size=\"sm\" variant=\"outline\" className=\"text-xs\">\n                      طلب تموين\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAkBe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,QAAQ,CAAC,SAAS;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;8BAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,KAAK,yBAAyB;;IACvC;IAEA,qBACE,6LAAC,kJAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAA0B;4CAC7B,aAAa,aAAa,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IAAI;;;;;;;kDAElE,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAG1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDAAU;wDAAQ,aAAa,QAAQ;;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAuB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC9E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQjF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAA+C;;;;;;8DAC5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA0C;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAgD;;;;;;8DAC7D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMzB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAiD;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhC,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA2C;;;;;;8DACxD,6LAAC;oDAAE,WAAU;8DAAiD;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;sEACtB,6LAAC;4DAAK,WAAU;sEAAqC;;;;;;sEACrD,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAGjD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ7B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ7C,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ7E,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ/E,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA2C;;;;;;sEACzD,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAC1C,6LAAC;4DAAO,MAAK;4DAAK,WAAU;sEAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAW/E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;gDACjB;;;;;;;sDAGR,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAqC;;;;;;;;;;;;;;;;;;0DAItD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAoC;;;;;;;;;;;;;;;;;;0DAIrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;;;;;;kEAErB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;0DAIvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;kEAExB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;0EACnD,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO7D,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;gDACrB;;;;;;;sDAGR,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAEnB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;kFAErB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAsC;;;;;;0FACnD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAGzC,6LAAC;gEAAK,WAAU;0EAAqE;;;;;;;;;;;;kEAIvF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;kFAAiB,6LAAC;wEAAK,WAAU;kFAAyB;;;;;;;;;;;;0EAClG,6LAAC;gEAAO,MAAK;gEAAK,SAAQ;gEAAU,WAAU;0EAAU;;;;;;;;;;;;;;;;;;0DAM5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;kFAErB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAsC;;;;;;0FACnD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAGzC,6LAAC;gEAAK,WAAU;0EAA2E;;;;;;;;;;;;kEAI7F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;kFAAiB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;0EACrG,6LAAC;gEAAO,MAAK;gEAAK,SAAQ;gEAAU,WAAU;0EAAU;;;;;;;;;;;;;;;;;;0DAM5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;kFAErB,6LAAC;;0FACC,6LAAC;gFAAE,WAAU;0FAAsC;;;;;;0FACnD,6LAAC;gFAAE,WAAU;0FAAwB;;;;;;;;;;;;;;;;;;0EAGzC,6LAAC;gEAAK,WAAU;0EAA2E;;;;;;;;;;;;kEAI7F,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;kFAAiB,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;0EACrG,6LAAC;gEAAO,MAAK;gEAAK,SAAQ;gEAAU,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5E;GAxYwB;;QACiB,6IAAA,CAAA,UAAO;QAC/B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}