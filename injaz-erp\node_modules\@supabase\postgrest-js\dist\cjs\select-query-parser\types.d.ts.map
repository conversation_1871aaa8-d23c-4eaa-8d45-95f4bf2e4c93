{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../src/select-query-parser/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,mBAAmB,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AAE1F,YAAY,EAAE,mBAAmB,EAAE,aAAa,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAA;AAE1E,oBAAY,+BAA+B,GAAG,OAAO,CAAA;AAErD,oBAAY,4BAA4B,GACpC,KAAK,GACL,KAAK,GACL,KAAK,GACL,KAAK,GACL,+BAA+B,CAAA;AAEnC,oBAAY,kBAAkB,GAAG,4BAA4B,CAAA;AAE7D,oBAAY,IAAI,GACZ,MAAM,GACN,MAAM,GACN,OAAO,GACP,IAAI,GACJ;IACE,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAA;CAChC,GACD,IAAI,EAAE,CAAA;AAEV,aAAK,sBAAsB,GAAG,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;AAExF,aAAK,sBAAsB,GACvB,OAAO,GACP,QAAQ,GACR,SAAS,GACT,MAAM,GACN,MAAM,GACN,QAAQ,GACR,MAAM,GACN,QAAQ,GACR,WAAW,GACX,aAAa,GACb,MAAM,GACN,QAAQ,CAAA;AAEZ,aAAK,0BAA0B,GAC3B,sBAAsB,GACtB,sBAAsB,GACtB,MAAM,GACN,MAAM,GACN,OAAO,GACP,MAAM,GACN,QAAQ,GACR,MAAM,CAAA;AAEV,aAAK,oBAAoB,GAAG,IAAI,0BAA0B,EAAE,CAAA;AAE5D,aAAK,0BAA0B,CAAC,CAAC,SAAS,0BAA0B,IAAI,CAAC,SAAS,MAAM,GACpF,OAAO,GACP,CAAC,SAAS,sBAAsB,GAChC,MAAM,GACN,CAAC,SAAS,sBAAsB,GAChC,MAAM,GACN,CAAC,SAAS,MAAM,GAAG,OAAO,GAC1B,IAAI,GACJ,CAAC,SAAS,MAAM,GAChB,SAAS,GACT,CAAC,SAAS,QAAQ,GAClB,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GACvB,OAAO,CAAA;AAEX,aAAK,eAAe,CAAC,CAAC,SAAS,MAAM,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;AAGxE,oBAAY,eAAe,GAAG,0BAA0B,GAAG,oBAAoB,CAAA;AAG/E,oBAAY,eAAe,CAAC,CAAC,SAAS,eAAe,IAAI,CAAC,SAAS,oBAAoB,GACnF,0BAA0B,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,EAAE,0BAA0B,CAAC,CAAC,CAAC,EAAE,GACrF,0BAA0B,CAAC,CAAC,CAAC,CAAA;AAGjC,oBAAY,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,SAAS,CACpF,CAAC,EAAE,MAAM,CAAC,KACP,IAAI,GACL,CAAC,GACD,KAAK,CAAA;AAET,oBAAY,MAAM,CAAC,CAAC,IAAI,mBAAmB,CAAC,CAAC,SAAS,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,MAAM,MAAM,CAAC,GAC9F,CAAC,GACD,KAAK,CAAA;AAET,oBAAY,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAA;AAGhD,oBAAY,YAAY,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,IAAI,CAAC,SAAS,IAAI,GAC/F,EAAE,GACF,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;AAExC,oBAAY,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC,CAAC,CAAC,CAAA;AAG7C,oBAAY,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS;KAAG,CAAC,IAAI,MAAM,CAAC,GAAG,MAAM,CAAC;CAAE,GAAG,CAAC,GAAG,KAAK,CAAA;AAGvF,oBAAY,YAAY,CAAC,CAAC,IAAI,IAAI,SAAS,CAAC,GAAG,IAAI,GAAG,KAAK,CAAA;AAE3D,oBAAY,eAAe,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,SAAS,CAAC,OAAO,EAAE,GAAG,OAAO,EAAE,CAAC,GAC3F,IAAI,GACJ,KAAK,CAAA;AAGT,oBAAY,cAAc,CAAC,MAAM,SAAS,aAAa,IAAI,MAAM,CAAC,QAAQ,CAAC,GACzE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC,CAAA;AAE9B,oBAAY,qBAAqB,CAC/B,MAAM,SAAS,aAAa,EAC5B,KAAK,SAAS,MAAM,IAClB,cAAc,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,SAAS;IAAE,aAAa,EAAE,MAAM,CAAC,CAAA;CAAE,GAAG,CAAC,GAAG,KAAK,CAAA"}